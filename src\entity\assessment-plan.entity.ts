import {
  Table,
  Column,
  Model,
  DataType,
  HasMany,
  HasOne,
} from 'sequelize-typescript';
import { AssessmentTask } from './assessment-task.entity';
import { IAssessmentPlan } from './interface';
import { Rule } from './rule.entity';
import { ScoreReport } from './score-report.entity';

@Table({
  tableName: 'assessment_plan',
  comment: '考核方案表',
  timestamps: true,
})
export class AssessmentPlan
  extends Model<AssessmentPlan>
  implements IAssessmentPlan
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '方案唯一标识',
  })
  planId: number;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    unique: {
      name: 'unique_title_enterprise_semester',
      msg: '同一学期已经相同名称的方案了',
    },
    comment: '方案名称（如"2025年第一学期教师月度考核"）',
  })
  planName: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: false,
    unique: {
      name: 'unique_title_enterprise_semester',
      msg: '同一学期已经相同名称的方案了',
    },
    comment: '学校编号',
  })
  enterpriseCode: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    unique: {
      name: 'unique_title_enterprise_semester',
      msg: '同一学期已经相同名称的方案了',
    },
    comment: '适用学期编号（如"2024202501"）',
  })
  semester: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '适用学期名称（如"25年上半学期"）',
  })
  semesterName: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    comment: '方案执行开始月份',
  })
  startMonth: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    comment: '方案执行结束月份',
  })
  endMonth: Date;

  @Column({
    type: DataType.JSON,
    allowNull: false,
    defaultValue: [],
    comment:
      '可填写日期数组，空数组表示整月都能填，数字数组表示只能在指定日期填写',
  })
  fillableDates: number[];

  @Column({
    type: DataType.ENUM('score', 'star'),
    allowNull: false,
    comment: '赋分形式（分值/星级）',
  })
  scoringType: 'score' | 'star';

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: '最大加分（仅分值形式有效）',
  })
  maxAdjustment?: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: '最大减分（仅分值形式有效）',
  })
  minAdjustment?: number;

  @Column({
    type: DataType.ENUM('grade', 'score'),
    allowNull: false,
    comment: '公示形式（等级/分数）',
  })
  publicationType: 'grade' | 'score';

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 5,
    comment: '最高星数（仅星级形式有效）',
  })
  maxStars?: number;

  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: [],
    comment: '等次规则（仅分数形式有效）',
  })
  gradeRules?: {
    /** 等次名称 */
    gradeName: string;
    /** 起始分数 */
    startScore: number;
    /** 结束分数 */
    endScore: number;
    /** 对应等级 */
    grade: string;
  }[];

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '方案描述',
  })
  description?: string;

  @Column({
    type: DataType.ENUM('draft', 'published'),
    allowNull: false,
    defaultValue: 'draft',
    comment: '状态（草稿/已发布）',
  })
  status: 'draft' | 'published';

  @HasMany(() => Rule)
  rules: Rule[];

  @HasMany(() => AssessmentTask)
  assessmentTasks: AssessmentTask[];

  @HasOne(() => Rule)
  rule: Rule;

  @HasMany(() => ScoreReport)
  scoreReports: ScoreReport[];
}
