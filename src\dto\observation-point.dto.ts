import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 创建观测点DTO
 */
export class CreateObservationPointDTO {
  @Rule(
    RuleType.array()
      .items(
        RuleType.object({
          pointName: RuleType.string()
            .required()
            .error(new Error('观测点名称不能为空')),
          description: RuleType.string()
            .min(0)
            .max(200)
            .optional()
            .error(new Error('评分说明不能超过200个字符')),
          baseScore: RuleType.number()
            .min(0)
            .required()
            .error(new Error('基础分值不能为空且必须大于等于0')),
        })
      )
      .required()
  )
  list: Array<{
    scoringId: number;
    pointName: string;
    description?: string;
    baseScore: number;
  }>;
}

/**
 * 更新观测点DTO
 */
export class UpdateObservationPointDTO {
  @Rule(RuleType.number().optional())
  scoringId?: number;

  @Rule(RuleType.string().optional())
  pointName?: string;

  @Rule(
    RuleType.string()
      .min(0)
      .max(200)
      .optional()
      .error(new Error('评分说明不能超过200个字符'))
  )
  description?: string;

  @Rule(
    RuleType.number()
      .min(0)
      .optional()
      .error(new Error('基础分值必须大于等于0'))
  )
  baseScore?: number;
}
