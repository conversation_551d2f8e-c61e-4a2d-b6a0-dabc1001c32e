import { BaseService } from '../common/BaseService';
import { PublicationRecord } from '../entity/publication-record.entity';
import { ModelCtor } from 'sequelize-typescript';
import { CustomError } from '../error/custom.error';
import { Inject, Provide } from '@midwayjs/core';
import { AssessmentTask } from '../entity/assessment-task.entity';
import { AssessmentPlan } from '../entity/assessment-plan.entity';
import { ScoreReportService } from './score-report.service';
import { Op, WhereOptions } from 'sequelize';
import { ScoreReport } from '../entity/score-report.entity';

/**
 * 公示记录服务
 */
@Provide()
export class PublicationRecordService extends BaseService<PublicationRecord> {
  @Inject()
  reportService: ScoreReportService;

  constructor() {
    super('公示记录');
  }

  /**
   * 获取模型
   */
  protected getModel(): ModelCtor<PublicationRecord> {
    return PublicationRecord;
  }

  /**
   * 发布公示记录
   * @param record 公示记录信息
   */
  async pubRecord(
    enterpriseCode: string,
    semester: string,
    month: number,
    startTime: Date,
    endTime: Date
  ) {
    console.log('开始发布成绩公示，条件：%j', {
      enterpriseCode,
      semester,
      month,
      startTime,
      endTime,
    });
    if (startTime >= endTime) {
      throw new CustomError('公示开始时间必须早于结束时间');
    }
    // 因为方案发布有时间重叠限制，所以这里查到的只能是一个
    const plant = await AssessmentPlan.findOne({
      where: {
        enterpriseCode,
        semester,
        status: 'published',
      },
      include: [
        {
          model: AssessmentTask,
          where: {
            month,
          },
          attributes: [], // 不返回关联的 AssessmentTask 记录的属性
          required: true,
        },
      ],
    });

    if (!plant) {
      throw new CustomError('指定方案不存在，不能进行公示操作');
    }

    // 先更新成绩报表数据
    await this.reportService.generateScoreReport(plant.planId, month);

    const existingRecord = await PublicationRecord.findOne({
      where: {
        planId: plant.planId,
        month,
      },
    });
    console.log('已生成报表数据');
    if (existingRecord) {
      await this.update(
        { pubId: existingRecord.pubId },
        { startTime, endTime }
      );
    } else {
      await this.create({
        planId: plant.planId,
        month,
        startTime,
        endTime,
      });
    }

    console.log('发布完成');
    return true;
  }

  async unpubRecord(enterpriseCode: string, semester: string, month: number) {
    console.log('开始撤消成绩公示，条件：%j', {
      enterpriseCode,
      semester,
      month,
    });
    await ScoreReport.destroy({
      where: {
        enterpriseCode,
        semester,
        month,
      },
    });
    console.log('已清除报表数据');
    const list = await PublicationRecord.findAll({
      where: {
        month,
      },
      include: [
        {
          model: AssessmentPlan,
          where: {
            enterpriseCode,
            semester,
          },
          attributes: [], // 不返回关联的 AssessmentTask 记录的属性
          required: true,
        },
      ],
    });
    for (let i = 0; i < list.length; i++) {
      const record = list[i];
      await record.destroy();
    }
    console.log('撤消完成');
    return true;
  }

  /**
   * 获取当前有效的公示记录
   */
  async findActiveRecords(
    enterpriseCode: string,
    semester?: string,
    month?: number
  ): Promise<PublicationRecord[]> {
    const where: WhereOptions<PublicationRecord> = {};
    const planWhere: WhereOptions<AssessmentPlan> = { enterpriseCode };
    if (semester && month) {
      where.month = month;
      planWhere.semester = semester;
    } else {
      const currentTime = new Date();
      where.startTime = { [Op.lte]: currentTime };
      where.endTime = { [Op.gte]: currentTime };
    }
    const list = await PublicationRecord.findAll({
      where,
      include: [
        {
          model: AssessmentPlan,
          where: planWhere,
          attributes: [], // 不返回关联的 AssessmentPlan 记录的属性
          required: true,
        },
      ],
    });
    return list;
  }
}
