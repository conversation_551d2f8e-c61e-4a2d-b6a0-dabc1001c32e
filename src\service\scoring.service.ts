import { BaseService } from '../common/BaseService';
import { Scoring } from '../entity/scoring.entity';
import { ModelCtor } from 'sequelize-typescript';
import { CustomError } from '../error/custom.error';
import { Provide } from '@midwayjs/core';
import { Rule } from '../entity/rule.entity';
import { AssessmentPlan } from '../entity/assessment-plan.entity';
import { ObservationPoint } from '../entity/observation-point.entity';

/**
 * 赋分规则服务
 */
@Provide()
export class ScoringService extends BaseService<Scoring> {
  constructor() {
    super('赋分规则');
  }

  /**
   * 获取模型
   */
  protected getModel(): ModelCtor<Scoring> {
    return Scoring;
  }

  /**
   * 创建赋分规则
   * @param scoring 赋分规则信息
   */
  async createScoring(scoring: Partial<Scoring>): Promise<Scoring> {
    // 验证赋分规则有效性
    await this.validateScoring(scoring, true);
    return this.create(scoring);
  }

  /**
   * 根据规则ID获取赋分规则列表
   * @param ruleId 规则ID
   */
  async findByRuleId(ruleId: number): Promise<Scoring[]> {
    const { list } = await this.findAll({
      query: { ruleId },
      include: [
        {
          association: 'observationPoints',
        },
      ],
    });
    return list;
  }

  /**
   * 检查方案配置完整性
   * @param scoringId 赋分规则ID
   * @returns boolean
   */
  async checkConfiguration(scoringId: number): Promise<boolean> {
    try {
      const scoring = await this.findById(scoringId);
      if (!scoring) {
        throw new CustomError('赋分规则不存在', 404);
      }
      if (scoring.assessorType === 'question') {
        // 问卷类型不用配置观测点
        return true;
      }
      // 检查是否存在未配置的观测点
      const observationPoints = await ObservationPoint.count({
        where: { scoringId },
      });
      return observationPoints > 0;
    } catch (error) {
      this.logger.error(
        {
          message: '检查方案配置完整性失败',
          source: '业务',
          operation: '查询',
          data: {
            scoringId,
          },
        },
        error
      );
      throw new CustomError('检查方案配置完整性失败', 500);
    }
  }

  /**
   * 验证赋分规则有效性
   * @param scoring 赋分规则信息
   * @Param [isCreate] 是否为创建流程
   */
  async validateScoring(
    scoring: Partial<Scoring>,
    isCreate?: true
  ): Promise<void> {
    // 验证规则是否存在
    if (scoring.ruleId) {
      const rule = await Rule.findByPk(scoring.ruleId, {
        include: [AssessmentPlan, Scoring],
      });

      if (!rule) {
        throw new CustomError('规则不存在', 404);
      }

      // 验证方案状态
      if (rule.assessmentPlan?.status === 'published') {
        throw new CustomError('已发布的方案不允许修改', 403);
      }

      if (isCreate && scoring.assessorType === 'question') {
        if (rule.scorings.some(s => s.assessorType === 'question')) {
          throw new CustomError('只能创建一个问卷类型的打分项');
        }
      }
    }

    // 验证评估者类型
    if (
      scoring.assessorType === 'role' &&
      (!scoring.roles || scoring.roles.length === 0)
    ) {
      throw new CustomError('角色类型的赋分组必须指定角色名称', 400);
    }

    if (
      scoring.assessorType === 'user' &&
      (!scoring.users || scoring.users.length === 0)
    ) {
      throw new CustomError('用户类型的赋分组必须指定用户ID', 400);
    }

    if (scoring.assessorType === 'mixed') {
      if (
        (!scoring.roles || scoring.roles.length === 0) &&
        (!scoring.users || scoring.users.length === 0)
      ) {
        throw new CustomError('混合类型的赋分组必须指定角色名称或用户ID', 400);
      }
    }
  }
}
