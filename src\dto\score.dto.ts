import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 创建评分DTO
 */
export class CreateScoreDTO {
  @Rule(RuleType.number().required().error(new Error('任务ID不能为空')))
  taskId: number;

  @Rule(RuleType.number().required().error(new Error('规则ID不能为空')))
  ruleId: number;

  @Rule(RuleType.number().optional().error(new Error('分值必须为数字')))
  scoreValue: number;

  @Rule(RuleType.string().max(255).optional())
  remark?: string;

  @Rule(RuleType.array().items(RuleType.string()).optional())
  files?: string[];
}

/**
 * 更新评分DTO
 */
export class UpdateScoreDTO {
  @Rule(RuleType.number().required().error(new Error('分值必须为数字')))
  scoreValue: number;

  @Rule(RuleType.string().max(255).optional())
  remark?: string;

  @Rule(RuleType.array().items(RuleType.string()).optional())
  files?: string[];
}
