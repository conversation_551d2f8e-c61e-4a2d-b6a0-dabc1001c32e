import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 创建考核任务DTO
 */
export class CreateAssessmentTaskDTO {
  @Rule(RuleType.number().required().error(new Error('方案ID不能为空')))
  planId: number;

  @Rule(RuleType.number().required().error(new Error('赋分规则ID不能为空')))
  scoringId: number;

  @Rule(
    RuleType.number()
      .integer()
      .min(1)
      .max(12)
      .required()
      .error(new Error('月份参数不合法'))
  )
  month: number;

  @Rule(RuleType.string().required().error(new Error('评考核人Code不能为空')))
  assessorCode: string;

  @Rule(RuleType.string().required().error(new Error('评考核人姓名不能为空')))
  assessorName: string;

  @Rule(RuleType.string().required().error(new Error('被评考核人Code不能为空')))
  assessedCode: string;

  @Rule(RuleType.string().required().error(new Error('被评考核人姓名不能为空')))
  assessedName: string;

  @Rule(RuleType.string().required().error(new Error('评分规则名称不能为空')))
  ruleName: string;

  @Rule(RuleType.string().required().error(new Error('赋分规则名称不能为空')))
  scoringName: string;

  @Rule(
    RuleType.string()
      .valid('pending', 'completed')
      .default('pending')
      .error(new Error('状态必须为pending或completed'))
  )
  status: 'pending' | 'completed';
}

/**
 * 更新考核任务DTO
 */
export class UpdateAssessmentTaskDTO {
  @Rule(RuleType.number().optional())
  planId?: number;

  @Rule(RuleType.number().optional())
  scoringId: number;

  @Rule(
    RuleType.number()
      .integer()
      .min(1)
      .max(12)
      .error(new Error('月份参数不合法'))
  )
  month?: number;

  @Rule(RuleType.string().optional())
  assessorCode: string;

  @Rule(RuleType.string().optional())
  assessorName: string;

  @Rule(RuleType.string().optional())
  assessedCode: string;

  @Rule(RuleType.string().optional())
  assessedName: string;

  @Rule(RuleType.string().optional())
  ruleName: string;

  @Rule(RuleType.string().optional())
  scoringName: string;

  @Rule(
    RuleType.string()
      .valid('pending', 'completed')
      .optional()
      .error(new Error('状态必须为pending或completed'))
  )
  status?: 'pending' | 'completed';
}
