/*
 * @Description: 使用xlsx生成excel文件，可以更好的控制样式
 * @Date: 2025-04-15 14:30:43
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-04-15 15:30:13
 */
import * as XLSX from 'xlsx';
// 引入 xlsx-style 库来支持样式
import * as XLSXStyle from 'xlsx-style';

export class XlsxUtils {
  /**
   * 将 JSON 数据转换为工作表，并支持合并单元格和表格美化
   * @param jsonData - JSON 数据
   * @param headerConfig - 表头配置，包含表头标签和数据映射
   * @param mergeConfigs - 合并单元格配置，格式为 { startRow, startCol, endRow, endCol } 的数组
   * @returns 工作表对象
   */
  static jsonToSheet({
    jsonData,
    headerConfig,
    mergeConfigs = [],
  }: {
    jsonData: any[];
    headerConfig: {
      // 表头用二维数组表示，以支持多级表头
      headers: string[][];
      dataKeys: (string | ((item: any, index: number) => any))[];
    };
    mergeConfigs?: {
      startRow: number;
      startCol: number;
      endRow: number;
      endCol: number;
    }[];
  }): XLSX.WorkSheet {
    const headers = headerConfig.headers;
    const data = jsonData.map((item, index) => {
      return headerConfig.dataKeys.map(key => {
        if (typeof key === 'function') {
          return key(item, index);
        }
        return item[key];
      });
    });

    const sheet = XLSX.utils.aoa_to_sheet([...headers, ...data]);

    // 合并单元格
    mergeConfigs.forEach(config => {
      this.mergeCells(
        sheet,
        config.startRow,
        config.startCol,
        config.endRow,
        config.endCol
      );
    });

    // 表格美化
    this.beautifySheet(sheet);

    return sheet;
  }

  static createBuffer(
    sheetList: { sheet: XLSX.WorkSheet; sheetName?: string }[]
  ) {
    const workbook = XLSX.utils.book_new();
    sheetList.forEach(({ sheet, sheetName }, index) => {
      XLSX.utils.book_append_sheet(
        workbook,
        sheet,
        sheetName || 'Sheet' + (index + 1)
      );
    });

    // 使用 xlsx-style 的 write 方法来保留样式
    const buffer = XLSXStyle.write(workbook, {
      type: 'buffer',
      bookType: 'xlsx',
    });
    return buffer;
  }

  /**
   * 合并单元格
   * @param sheet - 工作表对象
   * @param startRow - 起始行
   * @param startCol - 起始列
   * @param endRow - 结束行
   * @param endCol - 结束列
   */
  static mergeCells(
    sheet: XLSX.WorkSheet,
    startRow: number,
    startCol: number,
    endRow: number,
    endCol: number
  ) {
    if (!sheet['!merges']) {
      sheet['!merges'] = [];
    }
    sheet['!merges'].push({
      s: { r: startRow, c: startCol },
      e: { r: endRow, c: endCol },
    });
  }

  /**
   * 美化表格，添加线框，表头加粗加反色
   * @param sheet - 工作表对象
   */
  static beautifySheet(sheet: XLSX.WorkSheet) {
    if (!sheet['!ref']) return;

    const range = XLSX.utils.decode_range(sheet['!ref']);

    // 更明确的样式定义
    const headerStyle = {
      font: {
        bold: true,
        color: { rgb: 'FFFFFF' },
      },
      fill: {
        patternType: 'solid',
        fgColor: { rgb: '4472C4' },
      },
      border: {
        top: { style: 'thin', color: { auto: 1 } },
        bottom: { style: 'thin', color: { auto: 1 } },
        left: { style: 'thin', color: { auto: 1 } },
        right: { style: 'thin', color: { auto: 1 } },
      },
      alignment: {
        horizontal: 'center',
        vertical: 'center',
      },
    };

    const cellStyle = {
      border: {
        top: { style: 'thin', color: { auto: 1 } },
        bottom: { style: 'thin', color: { auto: 1 } },
        left: { style: 'thin', color: { auto: 1 } },
        right: { style: 'thin', color: { auto: 1 } },
      },
    };

    // 设置列宽
    const colWidth = [];
    for (let col = range.s.c; col <= range.e.c; col++) {
      colWidth.push({ wch: 15 }); // 设置默认列宽
    }
    sheet['!cols'] = colWidth;

    // 设置表头样式 - 对所有表头行应用样式
    const headerRows = sheet['!merges']
      ? Math.max(...sheet['!merges'].map(m => m.e.r)) + 1
      : 1;

    for (let row = range.s.r; row < headerRows; row++) {
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        if (!sheet[cellAddress]) {
          sheet[cellAddress] = { t: 's', v: '' };
        }
        sheet[cellAddress].s = headerStyle;
      }
    }

    // 设置数据单元格样式
    for (let row = headerRows; row <= range.e.r; row++) {
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        if (!sheet[cellAddress]) {
          sheet[cellAddress] = { t: 's', v: '' };
        }
        sheet[cellAddress].s = cellStyle;
      }
    }
  }
}
