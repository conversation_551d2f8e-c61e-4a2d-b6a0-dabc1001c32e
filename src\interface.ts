/**
 * 学校平台用户信息
 */
export interface IUserInfo {
  /** 编码 */
  code: string;
  /** 创建时间 */
  createdAt: string;
  /** 电子邮箱 */
  email: null | string;
  /** 就业状态 */
  employment_status: string;
  /** 企业ID */
  enterpriseId: string;
  /** 人脸识别MD5码 */
  facecodeMD5: null | string;
  /** 性别 */
  gender: null | string;
  /** ID */
  id: string;
  /** 是否托管 */
  isTrusteeship: boolean;
  /** 座机号码 */
  landline: null | string;
  /** 成员类型列表 */
  memberTypes: MemberType[];
  /** 手机号码 */
  mobile: null | string;
  /** 姓名 */
  name: string;
  /** 新编码 */
  newCode: string;
  /** 新工号 */
  newWorkCode: string;
  /** 组织成员列表 */
  organizationMembers: OrganizationMember[];
  /** 职位 */
  position?: string;
  /** 更新时间 */
  updatedAt: string;
  /** 用户名 */
  username: null | string;
  /** 工号 */
  work_code: string;
}

/**
 * 成员类型
 */
export interface MemberType {
  /** ID */
  id: string;
  /** 成员ID */
  memberId: string;
  /** 类型代码 */
  type_code: string;
  /** 类型名称 */
  type_name: string;
}

/**
 * 组织成员
 */
export interface OrganizationMember {
  /** ID */
  id: string;
  /** 成员ID */
  memberId: string;
  /** 组织信息 */
  organization: Organization;
  /** 组织ID */
  organizationId: string;
  /** 职位 */
  position?: string;
  /** 职位ID */
  positionId?: string;
}

/**
 * 组织信息
 */
export interface Organization {
  /** 编码 */
  code: string;
  /** 名称 */
  name: string;
}

/** 问卷信息 */
export interface IQuestionnaire {
  /** 问卷ID */
  id?: number;

  /** 问卷标题 */
  title: string;

  /** 问卷描述 */
  description: string;

  /** 问卷月份（YYYY-MM格式） */
  month: string;

  /** 问卷状态 */
  status: 'draft' | 'published' | 'closed';

  /** 星级模式（5星或10星） */
  star_mode: 5 | 10;

  /** 是否包含学校评价 */
  include_school_evaluation: boolean;

  /** SSO学校编码 */
  sso_school_code: string;

  /** SSO学校名称 */
  sso_school_name: string;

  /** 创建用户ID（SSO用户ID） */
  creator_user_id: string;

  /** 创建用户名称 */
  creator_user_name: string;

  /** 问卷开始时间 */
  start_time: Date;

  /** 问卷结束时间 */
  end_time: Date;

  /** 问卷说明/须知 */
  instructions: string;

  /** 是否允许匿名评价 */
  allow_anonymous: boolean;

  /** 最大评价教师数量限制（0表示无限制） */
  max_teachers_limit: number;
}

/** 教师问卷得分 */
export interface IQuestionScore {
  /** 平均得分 */
  average_score: number;
  /** 评价人数 */
  evaluation_count: number;
  /** 教师所属部门 */
  sso_teacher_department: string;
  /** 教师ID */
  sso_teacher_id: string;
  /** 教师姓名 */
  sso_teacher_name: string;
  /** 教师任教科目 */
  sso_teacher_subject: string;
}
