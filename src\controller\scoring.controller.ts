import {
  Inject,
  Controller,
  Post,
  Body,
  Param,
  HttpCode,
  Get,
  Put,
  Del,
} from '@midwayjs/core';
import { ScoringService } from '../service/scoring.service';
import { CreateScoringDTO, UpdateScoringDTO } from '../dto/scoring.dto';
import { CustomError } from '../error/custom.error';

@Controller('/rule/:ruleId/scoring')
export class ScoringController {
  @Inject()
  scoringService: ScoringService;

  @Post('/', { summary: '创建赋分规则' })
  @HttpCode(201)
  async create(
    @Param('ruleId') ruleId: number,
    @Body() body: CreateScoringDTO
  ) {
    const res = await this.scoringService.createScoring({ ...body, ruleId });
    return res;
  }

  @Get('/', { summary: '获取规则下的赋分规则列表' })
  async list(@Param('ruleId') ruleId: number) {
    try {
      const scorings = await this.scoringService.findByRuleId(ruleId);
      return { list: scorings };
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(`获取赋分规则列表失败: ${error.message}`, 500);
    }
  }

  @Get('/:scoringId', { summary: '获取赋分规则详情' })
  async getDetail(@Param('scoringId') scoringId: number) {
    try {
      const scoring = await this.scoringService.findById(scoringId);
      if (!scoring) {
        throw new CustomError('赋分规则不存在', 404);
      }
      return scoring;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(`获取赋分规则详情失败: ${error.message}`, 500);
    }
  }

  @Put('/:scoringId', { summary: '更新赋分规则' })
  async update(
    @Param('ruleId') ruleId: number,
    @Param('scoringId') scoringId: number,
    @Body() body: UpdateScoringDTO
  ) {
    try {
      // 验证赋分规则存在且属于指定规则
      const scoring = await this.scoringService.findById(scoringId);
      if (!scoring) {
        throw new CustomError('赋分规则不存在', 404);
      }
      await this.scoringService.validateScoring(scoring);
      await this.scoringService.update({ scoringId }, { ...body, ruleId });
      return true;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(`更新赋分规则失败: ${error.message}`, 500);
    }
  }

  @Del('/:scoringId', { summary: '删除赋分规则' })
  async delete(@Param('scoringId') scoringId: number) {
    // 验证赋分规则存在
    const scoring = await this.scoringService.findById(scoringId);
    if (!scoring) {
      throw new CustomError('赋分规则不存在', 404);
    }
    await this.scoringService.validateScoring(scoring);
    await this.scoringService.delete({ scoringId });
    return true;
  }

  @Get('/:scoringId/check', { summary: '检查赋分规则配置完整性' })
  async checkConfiguration(@Param('scoringId') scoringId: number) {
    return await this.scoringService.checkConfiguration(scoringId);
  }
}
