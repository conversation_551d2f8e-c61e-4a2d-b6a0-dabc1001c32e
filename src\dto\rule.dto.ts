import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 创建考核规则DTO
 */
export class CreateRuleDTO {
  @Rule(RuleType.number())
  planId: number;

  @Rule(RuleType.string().required().error(new Error('规则名称不能为空')))
  title: string;

  @Rule(
    RuleType.string()
      .valid('role', 'user')
      .required()
      .error(new Error('被评估类型必须为角色（role）或用户（user）'))
  )
  assessedType: 'role' | 'user';

  @Rule(
    RuleType.array()
      .items(
        RuleType.object().keys({
          roleName: RuleType.string().required(),
          roleCode: RuleType.string().required(),
        })
      )
      .optional()
      .error(new Error('角色名称列表格式不正确'))
  )
  roles?: {
    roleName: string;
    roleCode: string;
  }[];

  @Rule(
    RuleType.array()
      .items(
        RuleType.object().keys({
          userCode: RuleType.string().required(),
          userName: RuleType.string().required(),
        })
      )
      .optional()
      .error(new Error('用户ID列表格式不正确'))
  )
  users?: {
    userCode: string;
    userName: string;
  }[];

  @Rule(RuleType.string().optional())
  description?: string;
}

/**
 * 更新考核规则DTO
 */
export class UpdateRuleDTO {
  @Rule(RuleType.number().optional())
  planId?: number;

  @Rule(RuleType.string().optional())
  title?: string;

  @Rule(
    RuleType.string()
      .valid('role', 'user')
      .optional()
      .error(new Error('被评估类型必须为角色（role）或用户（user）'))
  )
  assessedType?: 'role' | 'user';

  @Rule(
    RuleType.array()
      .items(
        RuleType.object().keys({
          roleName: RuleType.string().required(),
          roleCode: RuleType.string().required(),
        })
      )
      .optional()
      .error(new Error('角色名称列表格式不正确'))
  )
  roles?: {
    roleCode: string;
    roleName: string;
  }[];

  @Rule(
    RuleType.array()
      .items(
        RuleType.object().keys({
          userCode: RuleType.string().required(),
          userName: RuleType.string().required(),
        })
      )
      .optional()
      .error(new Error('用户ID列表格式不正确'))
  )
  users?: {
    userCode: string;
    userName: string;
  }[];

  @Rule(RuleType.string().optional())
  description?: string;
}
