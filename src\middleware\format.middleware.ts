import { Middleware, IMiddleware } from '@midwayjs/core';
import { NextFunction, Context } from '@midwayjs/koa';

/**
 * 对接口返回的数据统一包装
 */
@Middleware()
export class FormatMiddleware implements IMiddleware<Context, NextFunction> {
  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      const result = await next();
      if (typeof result === 'object') {
        const { message, ...data } = result;
        return {
          errCode: 0,
          msg: message || 'OK',
          data,
        };
      }
      return {
        errCode: 0,
        msg: 'OK',
        data: result,
      };
    };
  }

  // match(ctx) {
  //   return ctx.path.indexOf('/api') === 0;
  // }

  static getName(): string {
    return 'API_RESPONSE_FORMAT';
  }
}
