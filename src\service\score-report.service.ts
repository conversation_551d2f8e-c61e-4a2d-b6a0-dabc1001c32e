import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { BaseService } from '../common/BaseService';
import { ScoreReport } from '../entity/score-report.entity';
import { AssessmentPlan } from '../entity/assessment-plan.entity';
import { AssessmentTask } from '../entity/assessment-task.entity';
import { Score } from '../entity/score.entity';
import { Rule } from '../entity/rule.entity';
import { Scoring } from '../entity/scoring.entity';
import { ScoreReportDetail } from '../entity/score-report-deatil.entity';
import { ObservationPoint } from '../entity/observation-point.entity';
import { AssessmentTaskService } from './assessment-task.service';
import { CustomLogger } from '../common/CustomLogger';
import { Op, WhereOptions } from 'sequelize';
import { ExcelColumnImportOptions } from '../common/ExcelUtil';
import { XlsxUtils } from '../common/xlsxUtils.ts';
import { QuestionnaireService } from './api_3th/questionnaire.service';
// import { build } from 'node-xlsx';

@Provide()
export class ScoreReportService extends BaseService<ScoreReport> {
  @Inject()
  ctx: Context;

  @Inject()
  assessmentTaskService: AssessmentTaskService;

  @Inject()
  logger: CustomLogger;

  @Inject()
  questionnaireService: QuestionnaireService;

  constructor() {
    super('成绩报表');
  }
  getModel = () => {
    return ScoreReport;
  };

  /**
   * 生成报表基础数据
   * @param plan 考核方案
   * @param month 考核月份
   * @return Promise<ScoreReport[]>
   */
  private async generateScoreReportBaseData(
    plan: AssessmentPlan,
    month: number
  ) {
    console.log('开始生成报表基础数据');
    // 使用事务确保数据一致性
    const transaction = await ScoreReport.sequelize.transaction();

    try {
      // 删除原有数据
      await ScoreReport.destroy({
        where: {
          planId: plan.planId,
          month,
        },
        transaction,
      });
      this.logger.info({
        source: '业务',
        operation: '生成报表基础数据',
        message: `删除方案${plan.planId}月份${month}的原有数据`,
      });

      // 获取所有考核规则
      const rules = await Rule.findAll({
        where: {
          planId: plan.planId,
        },
        transaction,
      });
      this.logger.info({
        source: '业务',
        operation: '生成报表基础数据',
        message: `获取到${rules.length}条考核规则`,
      });

      const scoreReports = [];
      // 遍历考核规则
      for (const rule of rules) {
        console.log('开始处理考核规则-%s', rule.title);
        const tasks = await AssessmentTask.findAll({
          where: {
            ruleId: rule.ruleId,
            month,
          },
          include: [
            {
              model: Score,
              include: [ObservationPoint],
            },
            Scoring,
          ],
          transaction,
        });
        this.logger.info({
          source: '业务',
          operation: '生成报表基础数据',
          message: `规则${rule.ruleId}获取到${tasks.length}个考核任务`,
        });

        // 遍历同一考核规则下的所有考核任务，创建成绩报表主体，计算各打分组总分
        const report_set = new Set<string>();
        const score_map = new Map<string, number[]>();
        for (const task of tasks) {
          report_set.add(`${task.assessedCode}-${task.assessedName}`);
          // 按不同打分方组汇总
          const key_score = `${task.assessedCode}-${task.scoringId}-${task.scoringName}-${task.scoring.weight}`;
          const scoreList = score_map.get(key_score) || [];
          // 没打分的情况下按基础分计算
          const score = task.scores.reduce(
            (acc, cur) =>
              acc + Number(cur.scoreValue || cur.observationPoint.baseScore),
            0
          );
          score_map.set(key_score, [...scoreList, score]);
        }

        const info = {
          enterpriseCode: plan.enterpriseCode,
          semester: plan.semester,
          semesterName: plan.semesterName,
          planId: plan.planId,
          planName: plan.planName,
          ruleId: rule.ruleId,
          ruleName: rule.title,
          month: month,
        };
        // 如果当前考核规则下配置了家长问卷，则需要再做问卷相关处理
        const question = await Scoring.findOne({
          where: {
            ruleId: rule.ruleId,
            assessorType: 'question',
          },
        });
        for (const item of Array.from(report_set)) {
          // console.log('开始汇总被考核人-%s', item.split('-')[1]);
          // 先创建主体，不计算总分
          const report = await ScoreReport.create(
            {
              ...info,
              assessedCode: item.split('-')[0],
              assessedName: item.split('-')[1],
              score: 0,
            },
            { transaction }
          );
          // 创建对应的详情记录
          const map_keys = Array.from(score_map.keys()).filter(
            key => key.split('-')[0] === item.split('-')[0]
          );
          await ScoreReportDetail.bulkCreate(
            map_keys.map(key => {
              const scoringId = Number(key.split('-')[1]);
              const scoringName = key.split('-')[2];
              const weight = Number(key.split('-')[3]);
              const scoreList = score_map.get(key);
              const score = scoreList.reduce(
                (acc, cur) => acc + Number(cur),
                0
              );

              const score_avg = scoreList.length ? score / scoreList.length : 0;
              return {
                reportId: report.reportId,
                scoringId,
                scoringName,
                weight,
                score: score_avg,
              };
            }),
            { transaction }
          );
          // console.log('被考核人-%s汇总完成', item.split('-')[1]);
          if (question) {
            // console.log('需要处理家长问卷');
            await ScoreReportDetail.create(
              {
                reportId: report.reportId,
                scoringId: question.scoringId,
                scoringName: question.title,
                weight: question.weight,
                isQuestion: true,
                // 默认100分
                score: 100,
              },
              { transaction }
            );
            // console.log('家长问卷处理完成');
          }
          scoreReports.push(report);
        }
      }

      // 提交事务
      await transaction.commit();
      this.logger.info({
        source: '业务',
        operation: '生成报表基础数据',
        message: `成功生成${scoreReports.length}条报表记录`,
      });
      console.log('报表基础数据生成成功'); // 打印成功信息，以便确认程序是否正常运行
      return scoreReports;
    } catch (error) {
      // 发生错误时回滚事务
      await transaction.rollback();
      this.logger.error(
        {
          source: '业务',
          operation: '生成报表基础数据',
          message: '生成报表基础数据失败',
        },
        error
      );
      throw error;
    }
  }

  /**
   * 生成报表
   * @param planId 考核方案ID
   * @param month 考核月份
   * @return Promise<void>
   * */
  async generateScoreReport(planId: number, month: number) {
    console.log('开始生成报表');
    const plan = await AssessmentPlan.findByPk(planId);
    if (!plan) {
      this.ctx.throw(404, '考核方案不存在');
    }

    const transaction = await ScoreReport.sequelize.transaction();
    try {
      // 生成基础数据
      const reportList = await this.generateScoreReportBaseData(plan, month);
      this.logger.info({
        source: '业务',
        operation: '生成报表',
        message: `开始计算${reportList.length}条报表的总分`,
      });

      // 使用事务更新总分
      for (const report of reportList) {
        const details = await ScoreReportDetail.findAll({
          where: {
            reportId: report.reportId,
          },
          transaction,
        });
        const score = details.reduce(
          (acc, cur) => acc + Number((cur.score * cur.weight) / 100),
          0
        );
        await report.update(
          {
            score,
          },
          { transaction }
        );
      }
      await transaction.commit();
      this.logger.info({
        source: '业务',
        operation: '生成报表',
        message: '生成报表成功',
      });
      console.log('报表生成成功'); // 打印成功信息，以便确认程序是否正常运行
    } catch (error) {
      await transaction.rollback();
      console.error(error); // 打印错误详细信息，以便排查问题
      // 发生错误时回滚事务
      this.logger.error(
        {
          source: '业务',
          operation: '生成报表',
          message: '生成报表失败',
        },
        error
      );
    }
  }

  /**
   * 获取报表分组
   * @param enterpriseCode 企业编号
   * @param semester 学期编号
   * @param month 考核月份
   * @return Promise<ScoreReport[]>
   * */
  async getScoreReportGroups(
    enterpriseCode: string,
    semester: string,
    month: number
  ) {
    // 以ruleName分组
    const list = await ScoreReport.findAll({
      attributes: ['ruleName'],
      where: {
        enterpriseCode,
        semester,
        month,
      },
      group: ['ruleName'],
    });
    return list.map(item => item.ruleName);
  }

  /**
   * 查询成绩报表
   * @param enterpriseCode 学校编号
   * @param semester 学期编号
   * @param month 月份
   * @param ruleId 考核分组ID
   * @param assessedName 被考核人姓名，模糊查询
   * @returns list
   */
  async getScoreReport(
    enterpriseCode: string,
    semester: string,
    month: number,
    ruleId: number,
    {
      assessedName,
    }: { assessedName?: string; isQuestion?: boolean } | undefined
  ) {
    const query = {
      enterpriseCode,
      semester,
      month,
    };
    if (ruleId !== 0) {
      // 约定ruleId为0时，查询所有分组
      query['ruleId'] = ruleId;
    }
    if (assessedName) {
      query['assessedName'] = {
        [Op.like]: `%${assessedName}%`,
      };
    }
    const include_detail = {
      model: ScoreReportDetail,
    };
    const res = await this.findAll({
      query,
      include: [include_detail],
      order: [
        ['month', 'DESC'],
        ['assessedCode', 'ASC'],
      ],
    });
    return res;
  }

  async getScoreReportForQuestion(
    enterpriseCode: string,
    semester: string,
    month: number,
    ruleId: number,
    { assessedName }: { assessedName?: string } | undefined
  ) {
    const query = {
      enterpriseCode,
      semester,
      month,
      ruleId,
    };
    if (assessedName) {
      query['assessedName'] = {
        [Op.like]: `%${assessedName}%`,
      };
    }

    const list = await ScoreReportDetail.findAll({
      where: {
        isQuestion: true,
      },
      include: [
        {
          model: ScoreReport,
          where: query,
        },
      ],
    });
    return { list };
  }

  /**
   * 按被考核人编号查询成绩报告
   * @param enterpriseCode 学校编号
   * @param semester 学期编号
   * @param assessedCode 被考核人编号
   * @returns res
   */
  async getScoreReportForOne(
    enterpriseCode: string,
    semester: string,
    assessedCode: string
  ) {
    const query = {
      enterpriseCode,
      semester,
      assessedCode,
    };
    const res = await this.findAll({
      query,
      include: [ScoreReportDetail],
      order: [['month', 'DESC']],
    });
    return res;
  }

  /**
   * 编辑问卷分数
   * @param detailId 记录ID
   * @param score 分数
   * @returns void
   */
  async editQuestionScore(detailId: number, score: number) {
    const detail = await ScoreReportDetail.findByPk(detailId);
    if (!detail) {
      this.ctx.throw(404, '记录不存在');
    }
    if (!detail.isQuestion) {
      this.ctx.throw(400, '不是问卷记录');
    }
    await detail.update({
      score,
    });
  }

  /**
   * 导出成绩报表
   * @param enterpriseCode 学校编号
   * @param semester 学期编号
   * @param month 月份
   * @returns void
   */
  private async getScoreReportAll(
    enterpriseCode: string,
    semester: string,
    month: number
  ) {
    const res_list = await ScoreReport.findAll({
      where: {
        enterpriseCode,
        semester,
        month,
      },
      order: [
        ['month', 'DESC'],
        ['assessedCode', 'ASC'],
      ],
    });

    const columns: ExcelColumnImportOptions[] = [
      {
        title: '序号',
        enName: 'No.',
      },
      {
        title: '姓名',
        enName: 'assessedName',
      },
      {
        title: '工号',
        enName: 'assessedCode',
      },
      {
        title: '总分',
        enName: 'score',
      },
    ];

    // 定义表头
    const headers = [
      // 第一行，需要合并单元格
      ['序号', '姓名', '工号', '总分'],
    ];

    // 处理数据
    const rows = res_list.map((item, index) => {
      const row = {
        'No.': index + 1,
        assessedName: item.assessedName,
        assessedCode: item.assessedCode,
        score: item.score,
      };

      // 返回行数据
      return row;
    });

    const sheet = XlsxUtils.jsonToSheet({
      jsonData: rows,
      headerConfig: {
        headers,
        dataKeys: columns.map(col => col.enName || col.title),
      },
    });
    return sheet;
  }

  /**
   * 按考核分组查询成绩报告，生成sheet
   * @param enterpriseCode 学校编号
   * @param semester 学期编号
   * @param month 月份
   * @param ruleId 考核分组ID
   * @returns sheet
   */
  private async getScoreReportSheet(
    enterpriseCode: string,
    semester: string,
    month: number,
    ruleId: number
  ) {
    // 获取成绩报表数据
    const { list } = await this.getScoreReport(
      enterpriseCode,
      semester,
      month,
      ruleId,
      {}
    );

    if (!list || list.length === 0) {
      throw new Error('没有找到符合条件的数据');
    }

    const columns: ExcelColumnImportOptions[] = [
      {
        title: '序号',
        enName: 'No.',
      },
      {
        title: '姓名',
        enName: 'assessedName',
      },
    ];
    const scores = list[0].scoreReportDetail.map(s => s.scoringName);
    scores.forEach(name => {
      columns.push({
        title: name,
      });
    });
    columns.push({
      title: '总分',
      enName: 'score',
    });

    // 定义表头
    const headers = [
      // 第一行，需要合并单元格
      [
        '序号',
        '姓名',
        '观测点',
        ...new Array(scores.length - 1).map(() => ''),
        '总分',
      ],
      // 第二行，主要显示观测点列
      ['', '', ...scores.map(s => s), ''],
    ];

    // 处理数据
    const rows = list.map((item, index) => {
      const row = {
        'No.': index + 1,
        assessedName: item.assessedName,
        score: item.score,
      };

      // 处理详情数据，将分数填充到对应的观测点
      if (item.scoreReportDetail && item.scoreReportDetail.length > 0) {
        item.scoreReportDetail.forEach(detail => {
          // 根据scoringName匹配对应的观测点
          if (scores.includes(detail.scoringName)) {
            row[detail.scoringName] = detail.score || '-';
          }
        });
      }

      // 返回行数据
      return row;
    });

    const sheet = XlsxUtils.jsonToSheet({
      jsonData: rows,
      headerConfig: {
        headers,
        dataKeys: columns.map(col => col.enName || col.title),
      },
      mergeConfigs: [
        // 合并"序号"和"姓名"单元格
        { startRow: 0, startCol: 0, endRow: 1, endCol: 0 },
        { startRow: 0, startCol: 1, endRow: 1, endCol: 1 },
        // 合并"观测点"单元格，合并列数等于scores数组长度
        {
          startRow: 0,
          startCol: 2,
          endRow: 0,
          endCol: 2 + scores.length - 1,
        },
        // 合并"总分"单元格
        {
          startRow: 0,
          startCol: 2 + scores.length,
          endRow: 1,
          endCol: 2 + scores.length,
        },
      ],
    });
    return sheet;
  }

  /**
   * 查询成绩报告打分详情，生成sheet
   * @param enterpriseCode 学校编号
   * @param semester 学期编号
   * @param month 月份
   * @param [ruleId] 考核分组ID，不传时查询所有分组
   * @returns sheet
   */
  private async getScoreReportDetailSheet(
    enterpriseCode: string,
    semester: string,
    month: number,
    ruleId?: number
  ) {
    const where = { month };
    if (ruleId) {
      where['ruleId'] = ruleId;
    }
    const list = await AssessmentTask.findAll({
      where,
      attributes: ['assessedName', 'assessorName', 'scoringName'],
      include: [
        {
          model: AssessmentPlan,
          where: {
            enterpriseCode,
            semester,
          },
          attributes: [],
        },
        {
          model: Score,
          attributes: ['scoreValue'],
          include: [
            {
              model: ObservationPoint,
              attributes: ['pointName', 'baseScore'],
            },
          ],
        },
        {
          model: Scoring,
          attributes: ['weight'],
        },
        {
          model: Rule,
          attributes: ['title'],
        },
      ],
      raw: true,
    });
    // console.log(list);
    const sheet = XlsxUtils.jsonToSheet({
      jsonData: list.map(item => ({
        ...item,
        'scoring.weight': item['scoring.weight'] + '%',
      })),
      headerConfig: {
        headers: [
          [
            '考核分组',
            '被考核人',
            '考核人',
            '打分分组',
            '观测点',
            '基础分',
            '比例',
            '得分',
          ],
        ],
        dataKeys: [
          'rule.title',
          'assessedName',
          'assessorName',
          'scoringName',
          'scores.observationPoint.pointName',
          'scores.observationPoint.baseScore',
          'scoring.weight',
          'scores.scoreValue',
        ],
      },
    });
    return sheet;
  }

  /**
   * 查询未填写人员名单，生成sheet
   * @param enterpriseCode 学校编号
   * @param semester 学期编号
   * @param month 月份
   * @param [ruleId] 考核分组ID，不传时查询所有分组
   * @returns sheet
   */
  private async getScoreReportUnfilledSheet(
    enterpriseCode: string,
    semester: string,
    month: number,
    ruleId?: number
  ) {
    const plans = await AssessmentPlan.findAll({
      where: {
        enterpriseCode,
        semester,
        status: 'published',
      },
      attributes: ['planId'],
    });
    const planIds = plans.map(plan => plan.planId);
    const queryOpt: WhereOptions = {
      planId: planIds,
      month,
      status: 'pending',
    };
    if (ruleId) {
      queryOpt.ruleId = ruleId;
    }
    const list = await AssessmentTask.findAll({
      where: queryOpt,
      attributes: ['assessorCode', 'assessorName'],
    });
    // list去重
    const uniqueList = [];
    list.forEach(item => {
      if (!uniqueList.find(i => i.assessorCode === item.assessorCode)) {
        uniqueList.push(item);
      }
    });
    // console.log(list);
    const sheet = XlsxUtils.jsonToSheet({
      jsonData: uniqueList.map(item => ({
        assessorCode: item.assessorCode,
        assessorName: item.assessorName,
      })),
      headerConfig: {
        headers: [['未完成人工号', '未完成人姓名']],
        dataKeys: ['assessorCode', 'assessorName'],
      },
    });
    return sheet;
  }

  /**
   * 导出成绩报表为Excel
   * @param enterpriseCode 学校编号
   * @param semester 学期编号
   * @param month 月份
   * @param ruleId 考核分组ID
   * @returns Buffer Excel文件Buffer
   */
  async exportScoreReportToExcel(
    enterpriseCode: string,
    semester: string,
    month: number,
    ruleId?: number
  ): Promise<Buffer> {
    try {
      const sheetList = [];
      if (ruleId) {
        // 分组导出
        const sheet1 = await this.getScoreReportSheet(
          enterpriseCode,
          semester,
          month,
          ruleId
        );
        sheetList.push({ sheet: sheet1, sheetName: '总分' });
      } else {
        const sheet_s = await this.getScoreReportAll(
          enterpriseCode,
          semester,
          month
        );
        sheetList.push({ sheet: sheet_s, sheetName: '总分' });
        // 全部导出
        const list = await this.assessmentTaskService.getGroups(
          enterpriseCode,
          semester,
          month
        );
        const sheets = await Promise.all(
          list.map(async ({ ruleId, ruleName }) => {
            const sheet = await this.getScoreReportSheet(
              enterpriseCode,
              semester,
              month,
              ruleId
            );
            return { sheet, sheetName: ruleName };
          })
        );
        sheetList.push(...sheets);
      }
      const sheet2 = await this.getScoreReportDetailSheet(
        enterpriseCode,
        semester,
        month,
        ruleId
      );
      sheetList.push({ sheet: sheet2, sheetName: '明细' });
      const buffer = XlsxUtils.createBuffer(sheetList);
      return buffer;
    } catch (error) {
      this.logger.error(
        {
          source: '业务',
          operation: '导出成绩报表',
          message: '导出成绩报表失败',
        },
        error
      );
      throw error;
    }
  }

  /**
   * 导出填写进度为Excel
   * @param enterpriseCode 学校编号
   * @param semester 学期编号
   * @param month 月份
   * @returns Buffer Excel文件Buffer
   */
  async exportTaskProgress(
    enterpriseCode: string,
    semester: string,
    month: number
  ): Promise<Buffer> {
    try {
      const sheetList = [];
      const sheet1 = await this.getScoreReportUnfilledSheet(
        enterpriseCode,
        semester,
        month
      );
      sheetList.push({ sheet: sheet1, sheetName: '未完成名单' });
      const sheet2 = await this.getScoreReportDetailSheet(
        enterpriseCode,
        semester,
        month
      );
      sheetList.push({ sheet: sheet2, sheetName: '填写进度' });
      const buffer = XlsxUtils.createBuffer(sheetList);
      return buffer;
    } catch (error) {
      this.logger.error(
        {
          source: '业务',
          operation: '导出成绩报表',
          message: '导出成绩报表失败',
        },
        error
      );
      throw error;
    }
  }

  /**
   * 同步问卷得分
   * @param enterpriseCode 企业编号
   * @param semester 学期编号
   * @param month 考核月份
   * @param ruleId 规则ID
   * @param questionnaireId 问卷ID
   * @returns 同步结果
   */
  async syncQuestionnaireScores(
    enterpriseCode: string,
    semester: string,
    month: number,
    ruleId: number,
    questionnaireId: number
  ) {
    // 获取当前考核系统中的问卷评分记录
    const { list: reportDetails } = await this.getScoreReportForQuestion(
      enterpriseCode,
      semester,
      month,
      ruleId,
      {}
    );

    if (!reportDetails || reportDetails.length === 0) {
      this.ctx.throw(404, '未找到问卷评分记录');
    }

    // 获取问卷系统中的教师得分
    const { list: questionScores } =
      await this.questionnaireService.getTeacherScoresByQuestionnaire(
        questionnaireId
      );

    if (!questionScores || questionScores.length === 0) {
      this.ctx.throw(404, '未找到问卷系统中的教师得分');
    }

    this.logger.info({
      source: '业务',
      operation: '同步问卷得分',
      message: `开始同步问卷得分，共有${reportDetails.length}条评分记录和${questionScores.length}条问卷得分`,
    });
    // console.log('开始同步问卷得分', questionScores);

    // 创建教师编号到问卷得分的映射
    const teacherScoreMap = new Map();
    questionScores.forEach(score => {
      teacherScoreMap.set(score.sso_teacher_id, score.average_score);
    });

    // 记录同步结果
    const result = {
      total: reportDetails.length,
      updated: 0,
      skipped: 0,
    };

    // 遍历问卷评分记录，更新得分
    for (const detail of reportDetails) {
      const teacherCode = detail.scoreReport.assessedCode;
      const questionScore = teacherScoreMap.get(teacherCode);

      if (questionScore !== undefined) {
        // 更新问卷得分
        await this.editQuestionScore(detail.detailId, questionScore);
        result.updated++;

        this.logger.info({
          source: '业务',
          operation: '同步问卷得分',
          message: `教师${teacherCode}的问卷得分已更新为${questionScore}`,
        });
      } else {
        result.skipped++;

        this.logger.warn({
          source: '业务',
          operation: '同步问卷得分',
          message: `未找到教师${teacherCode}的问卷得分`,
        });
      }
    }

    this.logger.info({
      source: '业务',
      operation: '同步问卷得分',
      message: `问卷得分同步完成，共更新${result.updated}条，跳过${result.skipped}条`,
    });

    return result;
  }
}
