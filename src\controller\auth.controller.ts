import { Body, Controller, Inject, Post } from '@midwayjs/core';
import { AuthService } from '../service/api_3th/auth.service';
import { SsoDto } from '../dto/sso.dto';

@Controller('/auth')
export class AuthController {
  @Inject()
  authService: AuthService;

  @Post('/data_conver_login')
  async data_conver_login(@Body() body: SsoDto) {
    const { enterpriseCode, userCode } = body;
    return await this.authService.postByUserCodes(enterpriseCode, userCode);
  }
}
