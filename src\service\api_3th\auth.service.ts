/*
 * @Description: 对接认证服务相关接口
 * @Date: 2025-03-31 11:54:45
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-03-31 12:24:05
 */
import { Inject, Provide } from '@midwayjs/core';
import { APIManager } from '../../common/APIManager';

@Provide()
export class AuthService {
  @Inject()
  apiManager: APIManager;

  /**
   * 解密
   *
   * @param {string} enterpriseCode 企业code
   * @param {string} userCode 用户code
   * @return {*} res
   * @memberof AuthService
   */
  async postByUserCodes(enterpriseCode: string, userCode: string) {
    const result = await this.apiManager.send({
      apiCode: 'postByUserCodes',
      body: {
        enterpriseCode,
        userCodes: [userCode],
      },
    });
    return result?.[0] || null;
  }
}
