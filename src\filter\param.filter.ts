/*
 * @description: 参数校验异常过滤器
 * @author: zpl
 * @Date: 2024-06-11 09:53:41
 * @LastEditTime: 2025-03-31 11:35:04
 * @LastEditors: 朱鹏亮 <EMAIL>
 */
import { Catch, Inject, MidwayHttpError } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { MidwayValidationError } from '@midwayjs/validate';
import { CustomLogger } from '../common/CustomLogger';

@Catch(MidwayValidationError)
export class ParamErrorFilter {
  @Inject()
  logger: CustomLogger;

  async catch(err: MidwayHttpError, ctx: Context) {
    // 所有的参数校验异常会到这里
    console.log('参数校验异常');
    console.error(err);
    this.logger.error({
      source: 'debug',
      message: '参数校验异常',
      details: err.message,
    });
    // ctx.status = err.status;
    if (err.message.includes('is not allowed')) {
      err.message = err.message.replace('is not allowed', '参数不允许');
    }
    return { errCode: err.status, msg: err.message };
  }
}
