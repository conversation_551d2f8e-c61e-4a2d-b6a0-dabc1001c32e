import { Processor, IProcessor } from '@midwayjs/bullmq';
import { AssessmentTaskService } from '../service/assessment-task.service';
import { Inject } from '@midwayjs/core';
import { AssessmentPlanService } from '../service/assessment-plan.service';
import { AssessmentTask } from '../entity/assessment-task.entity';
import { FORMAT } from '@midwayjs/core';

@Processor('generate-monthly-tasks', {
  repeat: {
    pattern: FORMAT.CRONTAB.EVERY_DAY,
  },
})
export class GenerateMonthlyTasksProcessor implements IProcessor {
  @Inject()
  assessmentPlanService: AssessmentPlanService;
  @Inject()
  assessmentTaskService: AssessmentTaskService;

  async execute() {
    console.log('generate-monthly-tasks start');
    // 查询所有已发布的方案
    const plans = await this.assessmentPlanService.findAll({
      query: { status: 'published' },
    });

    // 生成当月任务
    const currentMonth = new Date().getMonth() + 1;
    Promise.all(
      plans.list.map(async plan => {
        const planId = plan.planId;
        // 检查本月是否已生成过任务
        const existingTasks = await AssessmentTask.count({
          where: {
            planId,
            month: currentMonth,
          },
        });
        if (existingTasks > 0) {
          return;
        }
        // 创建任务
        await this.assessmentTaskService.generateMonthlyTasks(
          planId,
          currentMonth
        );
      })
    );
  }
}
