import {
  HttpServiceFactory,
  HttpService,
  AxiosResponse,
} from '@midwayjs/axios';
import { Inject, InjectClient, Provide } from '@midwayjs/core';
import { CustomLogger } from '../../common/CustomLogger';
import { IQuestionnaire, IQuestionScore } from '../../interface';

@Provide()
export class QuestionnaireService {
  @InjectClient(HttpServiceFactory, 'questionnaire')
  questionnaireAxios: HttpService;

  @Inject()
  logger: CustomLogger;

  private analisisResult(res: AxiosResponse<any>) {
    const { status, data } = res;
    if (status !== 200) {
      this.logger.error({
        message: '请求失败',
        source: '接口',
        data: {
          status,
          data,
        },
      });
      throw new Error('请求失败');
    }
    const { errCode, msg, data: returnData } = data;
    if (errCode !== 0) {
      console.log('问卷接口调用返回错误');
      console.log(data);
      this.logger.error({
        message: msg,
        source: '接口',
        data,
      });
      throw new Error(msg);
    }
    return returnData;
  }

  /**
   * 获取指定学校近1年内的问卷列表
   * @param schoolCode 学校编号
   * @returns 问卷列表
   */
  async getQuestionnairesBySchool(schoolCode: string) {
    const res = await this.questionnaireAxios.get(
      `/public/questionnaire/school/${schoolCode}`
    );
    const list = this.analisisResult(res) as IQuestionnaire[];
    return { list };
  }

  /**
   * 获取指定问卷的教师成绩统计
   * @param questionnaireId 问卷id
   * @returns 教师成绩统计列表
   */
  async getTeacherScoresByQuestionnaire(questionnaireId: number) {
    const res = await this.questionnaireAxios.get(
      `/public/questionnaire/${questionnaireId}/teacher-scores`
    );
    const list = this.analisisResult(res) as IQuestionScore[];
    return { list };
  }
}
