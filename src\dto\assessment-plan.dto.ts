import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 创建考核方案DTO
 */
export class CreateAssessmentPlanDTO {
  @Rule(RuleType.string().required().error(new Error('方案名称不能为空')))
  planName: string;

  @Rule(RuleType.string().required().error(new Error('学校编号不能为空')))
  enterpriseCode: string;

  @Rule(RuleType.string().required().error(new Error('学期不能为空')))
  semester: string;

  @Rule(RuleType.string().required().error(new Error('学期名称不能为空')))
  semesterName: string;

  @Rule(RuleType.date().required().error(new Error('开始月份不能为空')))
  startMonth: Date;

  @Rule(RuleType.date().required().error(new Error('结束月份不能为空')))
  endMonth: Date;

  @Rule(
    RuleType.array()
      .items(RuleType.number().integer().min(1).max(31))
      .optional()
  )
  fillableDates: number[];

  @Rule(
    RuleType.string()
      .required()
      .valid('score', 'star')
      .error(new Error('评分类型必须为分数制（score）或星级制（star）'))
  )
  scoringType: 'score' | 'star';

  @Rule(
    RuleType.number()
      .optional()
      .min(0)
      .error(new Error('最大调整值必须大于等于0'))
  )
  maxAdjustment?: number;

  @Rule(
    RuleType.number()
      .optional()
      .min(0)
      .error(new Error('最小调整值必须大于等于0'))
  )
  minAdjustment?: number;

  @Rule(
    RuleType.string()
      .default('score')
      .valid('grade', 'score')
      .error(new Error('公示类型必须为等级（grade）或分数（score）'))
  )
  publicationType: 'grade' | 'score';

  @Rule(
    RuleType.number()
      .optional()
      .integer()
      .min(1)
      .error(new Error('最大星级数必须为大于0的整数'))
  )
  maxStars?: number;

  @Rule(
    RuleType.array()
      .items(
        RuleType.object({
          gradeName: RuleType.string()
            .required()
            .error(new Error('等次名称不能为空')),
          startScore: RuleType.number()
            .required()
            .error(new Error('起始分数不能为空')),
          endScore: RuleType.number()
            .required()
            .error(new Error('结束分数不能为空')),
          grade: RuleType.string()
            .required()
            .error(new Error('对应等级不能为空')),
        })
      )
      .optional()
  )
  gradeRules?: {
    gradeName: string;
    startScore: number;
    endScore: number;
    grade: string;
  }[];

  @Rule(
    RuleType.string()
      .default('draft')
      .valid('draft', 'published')
      .error(new Error('状态必须为草稿（draft）或已发布（published）'))
  )
  status: 'draft' | 'published';
}

/**
 * 更新考核方案DTO
 */
export class UpdateAssessmentPlanDTO {
  @Rule(RuleType.string().optional())
  planName?: string;

  @Rule(RuleType.string().optional())
  enterpriseCode: string;

  @Rule(RuleType.string().optional())
  semester?: string;

  @Rule(RuleType.string().optional())
  semesterName?: string;

  @Rule(RuleType.date().optional())
  startMonth?: Date;

  @Rule(RuleType.date().optional())
  endMonth?: Date;

  @Rule(
    RuleType.array()
      .items(RuleType.number().integer().min(1).max(31))
      .optional()
  )
  fillableDates?: number[];

  @Rule(
    RuleType.string()
      .valid('score', 'star')
      .error(new Error('评分类型必须为分数制（score）或星级制（star）'))
      .optional()
  )
  scoringType?: 'score' | 'star';

  @Rule(
    RuleType.number()
      .min(0)
      .error(new Error('最大调整值必须大于等于0'))
      .optional()
  )
  maxAdjustment?: number;

  @Rule(
    RuleType.number()
      .min(0)
      .error(new Error('最小调整值必须大于等于0'))
      .optional()
  )
  minAdjustment?: number;

  @Rule(
    RuleType.string()
      .valid('grade', 'score')
      .error(new Error('公示类型必须为等级（grade）或分数（score）'))
      .optional()
  )
  publicationType?: 'grade' | 'score';

  @Rule(
    RuleType.number()
      .integer()
      .min(1)
      .error(new Error('最大星级数必须为大于0的整数'))
      .optional()
  )
  maxStars?: number;

  @Rule(RuleType.string().optional())
  description?: string;
}

export class UpdateAssessmentPlantStatusDTO {
  @Rule(
    RuleType.string()
      .valid('draft', 'published')
      .error(new Error('状态必须为草稿（draft）或已发布（published）'))
  )
  status?: 'draft' | 'published';

  @Rule(RuleType.string().optional())
  description?: string;
}

/**
 * 复制考核方案DTO
 */
export class CopyAssessmentPlanDTO {
  @Rule(RuleType.string().optional())
  planName?: string;

  @Rule(RuleType.string().optional())
  semester?: string;

  @Rule(RuleType.string().optional())
  semesterName?: string;

  @Rule(RuleType.date().optional())
  startMonth?: Date;

  @Rule(RuleType.date().optional())
  endMonth?: Date;

  @Rule(RuleType.string().optional())
  description?: string;
}
