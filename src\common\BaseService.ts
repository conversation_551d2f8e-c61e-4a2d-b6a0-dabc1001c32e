import { Model, ModelCtor } from 'sequelize-typescript';
import {
  Attributes,
  FindAttributeOptions,
  FindOptions,
  Includeable,
  Op,
  Order,
  Transaction,
  WhereOptions,
} from 'sequelize';
import { Inject } from '@midwayjs/core';
import { CustomLogger } from '../common/CustomLogger';
import { CustomError } from '../error/custom.error';

export abstract class BaseService<T extends Model> {
  @Inject()
  logger: CustomLogger;

  protected modelTitle: string;

  /**
   * Creates an instance of BaseService.
   * @param {string} modelTitle 模型名称，用于优化提示信息
   * @memberof BaseService
   */
  constructor(modelTitle: string) {
    this.modelTitle = modelTitle;
  }

  /**
   * 获取模型
   *
   * @abstract
   * @return {*}  {ModelCtor<T>} model
   * @memberof BaseService
   */
  protected abstract getModel(): ModelCtor<T>;

  /**
   * 处理查询条件
   *
   * @private
   * @param {WhereOptions<Attributes<T>>} query 原始查询条件
   * @param {any} filter 过滤条件
   * @return {WhereOptions<Attributes<T>>} 处理后的查询条件
   * @memberof BaseService
   */
  private processQueryConditions(
    query: WhereOptions<Attributes<T>>,
    filter?: any
  ): WhereOptions<Attributes<T>> {
    // 处理逗号分隔的字符串查询条件
    Object.keys(query).forEach(key => {
      if (typeof query[key] === 'string' && query[key].includes(',')) {
        query[key] = query[key].split(',');
      }
    });

    // 处理filter参数
    if (filter) {
      try {
        const filterObj = JSON.parse(filter);
        for (const key in filterObj) {
          const item = filterObj[key];
          if (Array.isArray(item)) {
            query[key] = {
              [Op.in]: item,
            };
          } else {
            if (item !== null) {
              query[key] = item;
            }
          }
        }
      } catch (error) {
        this.logger.warn(
          {
            serviceName: this.modelTitle,
            message: `Filter 解析失败: ${error.message}`,
            source: '数据库',
            operation: 'filter',
          },
          error
        );
      }
    }

    return {
      ...query,
      // 自定义查询参数
    };
  }

  /**
   * 处理排序条件
   *
   * @private
   * @param {Order} order 原始排序条件
   * @param {any} sort 排序字符串
   * @return {Order | undefined} 处理后的排序条件
   * @memberof BaseService
   */
  private processSortConditions(order?: Order, sort?: any): Order | undefined {
    if (order) {
      return order;
    } else if (sort) {
      try {
        const sortObj = JSON.parse(sort);
        return Object.keys(sortObj).map(key => [
          key,
          sortObj[key].includes('desc') ? 'DESC' : 'ASC',
        ]);
      } catch (error) {
        this.logger.error(
          {
            serviceName: this.modelTitle,
            message: `Sort 解析失败: ${error.message}`,
            source: '数据库',
            operation: 'sort',
          },
          error
        );
      }
    }
    return undefined;
  }

  /**
   * 查询列表
   *
   * @param {object} params params
   * @param {WhereOptions<Attributes<T>>} params.query 查询条件
   * @param {Includeable | Includeable[]} [params.include] 级联信息
   * @param {number} [params.offset] 分页
   * @param {number} [params.limit] 分页
   * @param {Order} [params.order] 分页
   * @return {Promise<{ list: T[]; total?: number }>}  res
   * @memberof BaseService
   */
  async findAll(params: {
    query: WhereOptions<Attributes<T>>;
    attributes?: FindAttributeOptions;
    include?: Includeable | Includeable[];
    offset?: number;
    limit?: number;
    order?: Order;
    sort?: any;
    filter?: any;
    transaction?: Transaction;
  }): Promise<{ list: T[]; total?: number }> {
    const {
      query,
      attributes,
      include,
      offset,
      limit,
      order,
      filter,
      sort,
      transaction,
    } = params;
    const CurrentModel = this.getModel();
    const queryOption: FindOptions<Attributes<T>> = {};
    const result: { list: T[]; total?: number } = { list: [] };

    try {
      if (attributes) {
        queryOption.attributes = attributes;
      }

      const hasPaging = offset !== undefined || limit !== undefined;
      if (hasPaging) {
        queryOption.offset = Number(offset) || 0;
        queryOption.limit = Number(limit) || 10;
      }

      // 处理查询条件
      const where = this.processQueryConditions(query, filter);
      queryOption.where = where;

      if (include) {
        queryOption.include = include;
      }

      // 处理排序条件
      const orderCondition = this.processSortConditions(order, sort);
      if (orderCondition) {
        queryOption.order = orderCondition;
      }

      // 添加事务支持
      if (transaction) {
        queryOption.transaction = transaction;
      }

      this.logger.debug({
        serviceName: this.modelTitle,
        message: `执行${this.modelTitle}查询`,
        source: '数据库',
        operation: 'findAll',
        data: { queryOption },
      });
      const res = await CurrentModel.findAll(queryOption);
      result.list = res;

      if (hasPaging) {
        // 获取总数
        const total = await CurrentModel.count({
          where,
          transaction,
        });
        result.total = total || 0;
      }
      return result;
    } catch (error) {
      const { transaction, ...queryInfo } = queryOption;
      void transaction; // 避免事务对象被记录在日志中
      this.logger.error(
        {
          serviceName: this.modelTitle,
          message: `${this.modelTitle}查询失败: ${error.message}`,
          source: '数据库',
          operation: 'findAll',
          data: { queryOption: queryInfo },
        },
        error
      );
      throw error;
    }
  }

  /**
   * 根据id查询
   *
   * @param {any} id id
   * @param {Transaction} [transaction] 事务
   * @return {*}  {(Promise<T | null>)} res
   * @memberof BaseService
   */
  async findById(id: any, transaction?: Transaction): Promise<T | null> {
    try {
      const CurrentModel = this.getModel();
      const res = await CurrentModel.findByPk(id, { transaction });
      return res;
    } catch (error) {
      this.logger.error(
        {
          serviceName: this.modelTitle,
          message: `根据ID查询${this.modelTitle}失败: ${error.message}`,
          source: '数据库',
          operation: 'findById',
          data: { id },
        },
        error
      );
      throw error;
    }
  }

  /**
   * 根据条件查询单个
   *
   * @param {Record<string, any>} where 查询条件
   * @param {Transaction} [transaction] 事务
   * @return {*}  {(Promise<T | null>)} res
   * @memberof BaseService
   */
  async findOne(
    where: Record<string, any>,
    transaction?: Transaction
  ): Promise<T | null> {
    try {
      const CurrentModel = this.getModel();
      const res = await CurrentModel.findOne({ where, transaction });
      return res;
    } catch (error) {
      this.logger.error(
        {
          serviceName: this.modelTitle,
          message: `查询单个${this.modelTitle}失败: ${error.message}`,
          source: '数据库',
          operation: 'findOne',
          data: { where },
        },
        error
      );
      throw error;
    }
  }

  /**
   * 创建
   *
   * @param {Partial<T>} info 创建数据
   * @param {Transaction} [transaction] 事务
   * @return {*}  {Promise<T>} res
   * @memberof BaseService
   */
  async create(info: Partial<T>, transaction?: Transaction): Promise<T> {
    try {
      const CurrentModel = this.getModel();
      const model = new CurrentModel();
      model.setAttributes(info);
      const res = await model.save({ transaction });
      this.logger.info({
        serviceName: this.modelTitle,
        message: `创建${this.modelTitle}成功`,
        source: '数据库',
        operation: 'create',
      });
      return res;
    } catch (error) {
      this.logger.error(
        {
          serviceName: this.modelTitle,
          message: `创建${this.modelTitle}失败: ${error.message}`,
          source: '数据库',
          operation: 'create',
          data: { info },
        },
        error
      );
      throw error;
    }
  }

  /**
   * 批量创建，数据量过大时，分批插入
   * @param info 数据
   * @param transaction 事务 可选参数
   * @param size 批量大小，默认200 可选参数
   * @return {Promise<void>}
   */
  async batchCreate(
    info: any[],
    transaction: Transaction | null = null,
    size = 200
  ): Promise<void> {
    const CurrentModel = this.getModel();
    try {
      this.logger.info({
        serviceName: this.modelTitle,
        message: `开始批量创建${this.modelTitle}`,
        source: '数据库',
        operation: 'batchCreate',
        data: {
          count: info.length,
          batchSize: size,
        },
      });

      if (info.length > size) {
        const batchCount = Math.ceil(info.length / size);
        this.logger.debug({
          serviceName: this.modelTitle,
          message: `数据量较大，分${batchCount}批处理`,
          source: '数据库',
          operation: 'batchCreate',
          data: {
            totalCount: info.length,
            batchSize: size,
          },
        });

        for (let i = 0; i < info.length; i += size) {
          const infoArr = info.slice(i, i + size);
          const batchIndex = Math.floor(i / size) + 1;
          this.logger.debug({
            serviceName: this.modelTitle,
            message: `处理第${batchIndex}/${batchCount}批数据`,
            source: '数据库',
            operation: 'batchCreate',
            data: { count: infoArr.length },
          });
          await CurrentModel.bulkCreate(infoArr, { transaction });
        }
      } else {
        await CurrentModel.bulkCreate(info, { transaction });
      }

      this.logger.info({
        serviceName: this.modelTitle,
        message: `批量创建${this.modelTitle}成功`,
        source: '数据库',
        operation: 'batchCreate',
        data: { count: info.length },
      });
    } catch (error) {
      this.logger.error(
        {
          serviceName: this.modelTitle,
          message: `批量创建${this.modelTitle}失败: ${error.message}`,
          source: '数据库',
          operation: 'batchCreate',
          data: { totalCount: info.length },
        },
        error
      );
      throw error;
    }
  }

  /**
   * 更新
   *
   * @param {Record<string, any>} where 查询条件
   * @param {Partial<T>} data 更新数据
   * @param {Transaction} [transaction] 事务
   * @return {Promise<T>} 更新后的记录
   * @memberof BaseService
   */
  async update(
    where: Record<string, any>,
    data: Partial<T>,
    transaction?: Transaction
  ): Promise<T> {
    try {
      const record = await this.findOne(where, transaction);
      if (!record) {
        this.logger.warn({
          serviceName: this.modelTitle,
          message: `更新失败：指定${this.modelTitle}不存在`,
          source: '数据库',
          operation: 'update',
          data: { where },
        });
        throw new CustomError(`指定${this.modelTitle}不存在`);
      }

      this.logger.debug({
        serviceName: this.modelTitle,
        message: `更新${this.modelTitle}`,
        source: '数据库',
        operation: 'update',
        data: { id: record.id, data },
      });
      const updated = await record.update(data, { transaction });
      this.logger.info({
        serviceName: this.modelTitle,
        message: `更新${this.modelTitle}成功`,
        source: '数据库',
        operation: 'update',
        data: { id: record.id },
      });
      return updated;
    } catch (error) {
      this.logger.error(
        {
          serviceName: this.modelTitle,
          message: `更新${this.modelTitle}失败: ${error.message}`,
          source: '数据库',
          operation: 'update',
          data: { where, data },
        },
        error
      );
      throw error;
    }
  }

  /**
   * 删除
   *
   * @param {Record<string, any>} where 查询条件
   * @param {Transaction} [transaction] 事务
   * @return {*}  {Promise<number>} 删除的记录数
   * @memberof BaseService
   */
  async delete(
    where: Record<string, any>,
    transaction?: Transaction
  ): Promise<number> {
    try {
      const CurrentModel = this.getModel();
      this.logger.debug({
        serviceName: this.modelTitle,
        message: `删除${this.modelTitle}`,
        source: '数据库',
        operation: 'delete',
        data: { where },
      });
      const count = await CurrentModel.destroy({ where, transaction });
      this.logger.info({
        serviceName: this.modelTitle,
        message: `删除${this.modelTitle}成功`,
        source: '数据库',
        operation: 'delete',
        data: { count },
      });
      return count;
    } catch (error) {
      this.logger.error(
        {
          serviceName: this.modelTitle,
          message: `删除${this.modelTitle}失败: ${error.message}`,
          source: '数据库',
          operation: 'delete',
          data: { where },
        },
        error
      );
      throw error;
    }
  }

  /**
   * 批量更新，数据量过大时，分批更新
   * @param info 数据
   * @param updateOption 更新选项
   * @param size 批量大小，默认200 可选参数
   * @return {Promise<void>}
   */
  async batchUpdate(
    info: any[],
    updateOption: {
      transaction?: Transaction | null;
      updateOnDuplicate: (keyof Attributes<T>)[];
    },
    size = 200
  ): Promise<void> {
    const CurrentModel = this.getModel();
    try {
      this.logger.info({
        serviceName: this.modelTitle,
        message: `开始批量更新${this.modelTitle}`,
        source: '数据库',
        operation: 'batchUpdate',
        data: {
          count: info.length,
          batchSize: size,
        },
      });

      if (info.length > size) {
        const batchCount = Math.ceil(info.length / size);
        this.logger.debug({
          serviceName: this.modelTitle,
          message: `数据量较大，分${batchCount}批处理`,
          source: '数据库',
          operation: 'batchUpdate',
          data: {
            totalCount: info.length,
            batchSize: size,
          },
        });

        for (let i = 0; i < info.length; i += size) {
          const infoArr = info.slice(i, i + size);
          const batchIndex = Math.floor(i / size) + 1;
          this.logger.debug({
            serviceName: this.modelTitle,
            message: `处理第${batchIndex}/${batchCount}批数据`,
            source: '数据库',
            operation: 'batchUpdate',
            data: {
              count: infoArr.length,
            },
          });
          await CurrentModel.bulkCreate(infoArr, updateOption);
        }
      } else {
        await CurrentModel.bulkCreate(info, updateOption);
      }

      this.logger.info({
        serviceName: this.modelTitle,
        message: `批量更新${this.modelTitle}成功`,
        source: '数据库',
        operation: 'batchUpdate',
        data: {
          count: info.length,
        },
      });
    } catch (error) {
      this.logger.error(
        {
          serviceName: this.modelTitle,
          message: `批量更新${this.modelTitle}失败: ${error.message}`,
          source: '数据库',
          operation: 'batchUpdate',
          data: { totalCount: info.length },
        },
        error
      );
      throw error;
    }
  }

  /**
   * 创建事务
   *
   * @return {Promise<Transaction>} 事务对象
   * @memberof BaseService
   */
  async createTransaction(): Promise<Transaction> {
    try {
      const CurrentModel = this.getModel();
      const transaction = await CurrentModel.sequelize.transaction();
      this.logger.debug({
        serviceName: this.modelTitle,
        message: '创建事务成功',
        source: '数据库',
        operation: 'transaction',
      });
      return transaction;
    } catch (error) {
      this.logger.error(
        {
          serviceName: this.modelTitle,
          message: `创建事务失败: ${error.message}`,
          source: '数据库',
          operation: 'transaction',
        },
        error
      );
      throw new Error(`创建事务失败: ${error.message}`);
    }
  }
}
