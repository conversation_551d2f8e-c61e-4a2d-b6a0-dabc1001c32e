import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { AssessmentPlan } from './assessment-plan.entity';
import { Score } from './score.entity';
import { IAssessmentTask } from './interface';
import { Scoring } from './scoring.entity';
import { Rule } from './rule.entity';

@Table({
  tableName: 'assessment_task',
  comment: '考核任务表',
  timestamps: true,
})
export class AssessmentTask
  extends Model<AssessmentTask>
  implements IAssessmentTask
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '任务唯一标识',
  })
  taskId: number;

  @ForeignKey(() => AssessmentPlan)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '所属方案ID',
  })
  planId: number;

  /** 考核规则ID */
  @ForeignKey(() => Rule)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '考核规则ID',
  })
  ruleId: number;

  @ForeignKey(() => Scoring)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_scoring_month_assessor_assessed',
      msg: '已存在相同的考核任务',
    },
    comment: '赋分规则ID',
  })
  scoringId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_scoring_month_assessor_assessed',
      msg: '已存在相同的考核任务',
    },
    comment: '考核月份',
  })
  month: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    unique: {
      name: 'unique_scoring_month_assessor_assessed',
      msg: '已存在相同的考核任务',
    },
    comment: '考核人编号或用户名',
  })
  assessorCode: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '考核人姓名',
  })
  assessorName: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    unique: {
      name: 'unique_scoring_month_assessor_assessed',
      msg: '已存在相同的考核任务',
    },
    comment: '被考核人编号或用户名',
  })
  assessedCode: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '被考核人姓名',
  })
  assessedName: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '评分规则名称',
  })
  ruleName: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '赋分规则名称',
  })
  scoringName: string;

  @Column({
    type: DataType.ENUM('pending', 'completed'),
    allowNull: false,
    comment: '任务状态',
  })
  status: 'pending' | 'completed';

  @BelongsTo(() => AssessmentPlan)
  assessmentPlan: AssessmentPlan;

  @BelongsTo(() => Rule, { onDelete: 'CASCADE' })
  rule: Rule;

  @BelongsTo(() => Scoring, { onDelete: 'CASCADE' })
  scoring: Scoring;

  @HasMany(() => Score)
  scores: Score[];
}
