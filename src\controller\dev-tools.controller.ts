import { Controller, Inject, Param, Put, Query } from '@midwayjs/core';
import { AssessmentTaskService } from '../service/assessment-task.service';
import { DevTestMiddleware } from '../middleware/devTest';

@Controller('/dev-tools')
export class DevToolsController {
  @Inject()
  assessmentTaskService: AssessmentTaskService;

  @Put('/generate-monthly-tasks/:planId', {
    middleware: [DevTestMiddleware],
    summary: '根据考核方案生成指定月份的考核任务',
  })
  async generateMonthlyTasks(
    @Param('planId') planId: number,
    @Query('month') month: number
  ) {
    return this.assessmentTaskService.generateMonthlyTasks(planId, month);
  }
}
