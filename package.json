{"name": "teacher-evaluation", "version": "1.0.0", "description": "", "private": true, "dependencies": {"@midwayjs/axios": "^3.20.3", "@midwayjs/bootstrap": "^3.12.0", "@midwayjs/bullmq": "^3.20.5", "@midwayjs/busboy": "^3.20.3", "@midwayjs/core": "^3.12.0", "@midwayjs/info": "^3.12.0", "@midwayjs/jwt": "^3.20.3", "@midwayjs/koa": "^3.12.0", "@midwayjs/logger": "^3.1.0", "@midwayjs/sequelize": "^3.20.3", "@midwayjs/validate": "^3.20.3", "i": "^0.3.7", "lodash": "^4.17.21", "mysql2": "^3.14.0", "node-xlsx": "^0.24.0", "npm": "^11.4.2", "sequelize": "^6.37.6", "sequelize-typescript": "^2.1.6", "uuid": "^11.1.0", "xlsx": "^0.20.2", "xlsx-style": "^0.8.13"}, "devDependencies": {"@midwayjs/mock": "^3.12.0", "@types/jest": "^29.2.0", "@types/lodash": "^4.17.16", "@types/node": "14", "cross-env": "^6.0.0", "jest": "^29.2.2", "mwts": "^1.3.0", "mwtsc": "^1.4.0", "ts-jest": "^29.0.3", "ts-node": "^10.9.2", "typescript": "~4.8.0"}, "engines": {"node": ">=12.0.0"}, "scripts": {"start": "NODE_ENV=production node ./bootstrap.js", "dev": "cross-env NODE_ENV=local mwtsc --watch --run @midwayjs/mock/app.js", "test": "cross-env NODE_ENV=unittest jest", "cov": "jest --coverage", "lint": "mwts check", "lint:fix": "mwts fix", "ci": "npm run cov", "build": "mwtsc --cleanOutDir"}, "repository": {"type": "git", "url": ""}, "author": "anonymous", "license": "MIT"}