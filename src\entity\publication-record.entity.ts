import {
  Column,
  Model,
  Table,
  ForeignKey,
  BelongsTo,
  DataType,
} from 'sequelize-typescript';
import { AssessmentPlan } from './assessment-plan.entity';
import { IPublicationRecord } from './interface';

/**
 * 公示记录实体 - 管理某个考核方案在某个月是否公示考核结果
 */
@Table({
  tableName: 'publication_record',
  comment: '公示记录',
  timestamps: true,
})
export class PublicationRecord
  extends Model<PublicationRecord>
  implements IPublicationRecord
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '公示记录唯一标识',
  })
  pubId: number;

  @ForeignKey(() => AssessmentPlan)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_plan_month',
      msg: '已存在相同的公示记录',
    },
    comment: '关联的考核方案ID',
  })
  planId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_plan_month',
      msg: '已存在相同的公示记录',
    },
    comment: '公示月份',
  })
  month: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    comment: '公示开始时间',
  })
  startTime: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    comment: '公示结束时间',
  })
  endTime: Date;

  @BelongsTo(() => AssessmentPlan)
  assessmentPlan: AssessmentPlan;
}
