import { Logger, Provide, Scope, ScopeEnum, App } from '@midwayjs/core';
import { ILogger } from '@midwayjs/logger';
import { Application, Context } from '@midwayjs/koa';
import { v4 as uuidv4 } from 'uuid';

type sourceType =
  | '认证'
  | 'debug'
  | '系统'
  | '数据库'
  | '接口'
  | '业务'
  | '安全'
  | '性能'
  | '其他';
export type LoggerMsgProps = {
  /** 日志内容 */
  message: string;
  /** 日志来源 */
  source: sourceType;
  /** 服务名称 */
  serviceName?: string;
  /** 用户名 */
  username?: string;
  /** 错误详情 */
  details?: string;
  /** 请求ID，用于跟踪单个请求的完整生命周期 */
  requestId?: string;
  /** 跟踪ID，用于跟踪分布式系统中的调用链 */
  traceId?: string;
  /** 模块名称 */
  module?: string;
  /** 操作类型 */
  operation?: string;
  /** 额外数据，可以是任何JSON可序列化的数据 */
  data?: Record<string, any>;
};

@Provide()
@Scope(ScopeEnum.Singleton, { allowDowngrade: true })
export class CustomLogger implements ILogger {
  @Logger()
  logger: ILogger;

  @App()
  app: Application;

  private ctx: Context | null = null;

  /**
   * 设置当前请求上下文
   *
   * @param {Context} ctx Koa上下文
   * @memberof CustomLogger
   */
  setContext(ctx: Context) {
    this.ctx = ctx;
  }

  /**
   * 获取当前请求上下文中的请求ID
   *
   * @private
   * @return {string} 请求ID
   * @memberof CustomLogger
   */
  private getRequestId(): string {
    if (this.ctx) {
      // 如果上下文中已有requestId则使用它
      if (this.ctx.requestId) {
        return this.ctx.requestId;
      }
      // 否则生成一个新的并保存到上下文中
      const requestId = uuidv4();
      this.ctx.requestId = requestId;
      return requestId;
    }
    // 如果没有上下文，生成一个新的
    return uuidv4();
  }

  /**
   * 格式化日志消息，添加额外的结构化信息
   *
   * @private
   * @param {LoggerMsgProps} msg 原始日志信息
   * @return {*} 格式化后的日志对象
   * @memberof CustomLogger
   */
  private formatLogMessage(msg: LoggerMsgProps): any {
    const timestamp = new Date().toISOString();
    const requestId = msg.requestId || this.getRequestId();
    const traceId = msg.traceId || requestId;

    // 构建基础日志结构
    const logObject = {
      timestamp,
      level: '', // 将在各日志方法中设置
      message: msg.message,
      source: msg.source,
      service: msg.serviceName,
      requestId,
      traceId,
      data: msg.data || {},
      // ...msg,
    };

    // 添加请求信息（如果有上下文）
    if (this.ctx) {
      logObject['request'] = {
        url: this.ctx.url,
        method: this.ctx.method,
        ip: this.ctx.ip,
        userAgent: this.ctx.headers['user-agent'],
      };
    }

    return logObject;
  }

  /**
   * 信息日志
   *
   * @param {LoggerMsgProps} msg 日志信息
   * @param {...any[]} args
   * @memberof CustomLogger
   */
  public info(msg: LoggerMsgProps, ...args: any[]) {
    const formattedMsg = this.formatLogMessage(msg);
    formattedMsg.level = 'info';
    this.logger.info(formattedMsg, ...args);
  }
  /**
   * 调试日志
   *
   * @param {LoggerMsgProps} msg 日志信息
   * @param {...any[]} args 日志参数
   * @memberof CustomLogger
   */
  public debug(msg: LoggerMsgProps, ...args: any[]) {
    const formattedMsg = this.formatLogMessage(msg);
    formattedMsg.level = 'debug';
    this.logger.debug(formattedMsg, ...args);
  }
  /**
   * 错误日志
   *
   * @param {LoggerMsgProps} msg 日志信息
   * @param {...any[]} args 日志参数
   * @memberof CustomLogger
   */
  public error(msg: LoggerMsgProps, ...args: any[]) {
    const formattedMsg = this.formatLogMessage(msg);
    formattedMsg.level = 'error';

    // 如果有错误对象，提取更多信息
    if (args.length > 0 && args[0] instanceof Error) {
      const err = args[0];
      formattedMsg.error = {
        name: err.name,
        message: err.message,
      };
    }

    this.logger.error(formattedMsg, ...args);
  }
  /**
   * 警告日志
   *
   * @param {LoggerMsgProps} msg 日志信息
   * @param {...any[]} args 日志参数
   * @memberof CustomLogger
   */
  public warn(msg: LoggerMsgProps, ...args: any[]) {
    const formattedMsg = this.formatLogMessage(msg);
    formattedMsg.level = 'warn';

    // 如果有错误对象，提取更多信息
    if (args.length > 0 && args[0] instanceof Error) {
      const err = args[0];
      formattedMsg.error = {
        name: err.name,
        message: err.message,
      };
    }

    this.logger.warn(formattedMsg, ...args);
  }

  public write(msg: LoggerMsgProps, ...args: any[]) {
    const formattedMsg = this.formatLogMessage(msg);
    formattedMsg.level = 'write';
    this.logger.write(formattedMsg, ...args);
  }

  public verbose(msg: LoggerMsgProps, ...args: any[]) {
    const formattedMsg = this.formatLogMessage(msg);
    formattedMsg.level = 'verbose';
    this.logger.verbose(formattedMsg, ...args);
  }

  /**
   * 记录HTTP请求日志
   *
   * @param {Context} ctx Koa上下文
   * @memberof CustomLogger
   */
  public logHttpRequest(ctx: Context) {
    this.setContext(ctx);
    const requestTime = new Date();
    const requestId = this.getRequestId();

    console.dir({
      url: ctx.request.url,
      method: ctx.request.method,
      body: ctx.request.body || {},
      query: ctx.request.query || {},
      params: ctx.params,
      // headers: ctx.request.headers,
      ip: ctx.request.ip,
    });

    // 记录请求开始
    this.info({
      message: `HTTP ${ctx.method} ${ctx.url}`,
      source: '接口',
      operation: 'request',
      requestId,
      data: {
        query: ctx.query,
        body: ctx.request.body,
        params: ctx.params,
        ip: ctx.ip,
        userAgent: ctx.headers['user-agent'],
      },
    });

    // 返回一个函数用于记录响应
    return () => {
      const responseTime = new Date();
      const duration = responseTime.getTime() - requestTime.getTime();

      this.info({
        message: `HTTP ${ctx.method} ${ctx.url} ${ctx.status}`,
        source: '接口',
        operation: 'response',
        requestId,
        data: {
          status: ctx.status,
          duration: `${duration}ms`,
          responseSize: ctx.response.length || 0,
        },
      });
    };
  }
}
