import {
  Inject,
  Controller,
  Post,
  Body,
  Param,
  HttpCode,
  Get,
  Put,
  Del,
} from '@midwayjs/core';
import { RuleService } from '../service/rule.service';
import { CreateRuleDTO } from '../dto/rule.dto';
import { CustomError } from '../error/custom.error';

/**
 * 规则控制器
 */
@Controller('/assessment-plans/:planId/rule')
export class PlanRuleController {
  @Inject()
  ruleService: RuleService;

  @Post('/', { summary: '创建规则' })
  @HttpCode(201)
  async create(@Param('planId') planId: number, @Body() body: CreateRuleDTO) {
    // 确保planId与路径参数一致
    body.planId = planId;
    return this.ruleService.createRule(body);
  }

  @Get('/', { summary: '获取方案下的规则列表' })
  async list(@Param('planId') planId: number) {
    const rules = await this.ruleService.findByPlanId(planId);
    return { total: rules?.length, list: rules };
  }

  @Get('/:ruleId', { summary: '获取规则详情' })
  async getDetail(
    @Param('planId') planId: number,
    @Param('ruleId') ruleId: number
  ) {
    const rule = await this.ruleService.findById(ruleId);
    if (!rule) {
      throw new CustomError('规则不存在', 404);
    }
    if (rule.planId !== planId) {
      throw new CustomError('规则不属于指定方案', 400);
    }
    return rule;
  }

  @Put('/:ruleId', { summary: '更新规则' })
  async update(
    @Param('planId') planId: number,
    @Param('ruleId') ruleId: number,
    @Body() body: CreateRuleDTO
  ) {
    // 确保planId与路径参数一致
    body.planId = planId;

    // 验证规则存在且属于指定方案
    const rule = await this.ruleService.findById(ruleId);
    if (!rule) {
      throw new CustomError('规则不存在', 404);
    }
    if (rule.planId !== planId) {
      throw new CustomError('规则不属于指定方案', 400);
    }
    await this.ruleService.validateRule(rule);
    await this.ruleService.update({ ruleId }, body);
    return true;
  }

  @Del('/:ruleId', { summary: '删除规则' })
  async delete(
    @Param('planId') planId: number,
    @Param('ruleId') ruleId: number
  ) {
    // 验证规则存在且属于指定方案
    const rule = await this.ruleService.findById(ruleId);
    if (!rule) {
      throw new CustomError('规则不存在', 404);
    }
    if (rule.planId !== planId) {
      throw new CustomError('规则不属于指定方案', 400);
    }
    await this.ruleService.validateRule(rule);
    await this.ruleService.delete({ ruleId });
    return true;
  }

  @Get('/:ruleId/check', { summary: '检查规则配置完整性' })
  async checkConfiguration(@Param('ruleId') ruleId: number) {
    return this.ruleService.checkConfiguration(ruleId);
  }
}
