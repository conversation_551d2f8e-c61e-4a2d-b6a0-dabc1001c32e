import {
  Inject,
  Controller,
  Post,
  Body,
  Param,
  Get,
  Query,
} from '@midwayjs/core';
import { PublicationRecordService } from '../service/publication-record.service';
import { CreatePublicationRecordDTO } from '../dto/publication-record.dto';

/**
 * 公示记录控制器
 */
@Controller('/publication')
export class PublicationRecordController {
  @Inject()
  publicationRecordService: PublicationRecordService;

  /**
   * 发布公示记录
   * @param record 公示记录信息
   */
  @Post('/pub/:enterpriseCode/:semester/:month')
  async pubRecord(
    @Param('enterpriseCode') enterpriseCode: string,
    @Param('semester') semester: string,
    @Param('month') month: number,
    @Body() record: CreatePublicationRecordDTO
  ) {
    return this.publicationRecordService.pubRecord(
      enterpriseCode,
      semester,
      month,
      record.startTime,
      record.endTime
    );
  }

  /**
   * 撤销公示记录
   * @param pubId 公示记录ID
   */
  @Post('/unpub/:enterpriseCode/:semester/:month')
  async unpubRecord(
    @Param('enterpriseCode') enterpriseCode: string,
    @Param('semester') semester: string,
    @Param('month') month: number
  ) {
    return this.publicationRecordService.unpubRecord(
      enterpriseCode,
      semester,
      month
    );
  }

  @Get('/actived/:enterpriseCode', {
    summary: '获取当前有效的公示记录',
    description:
      '管理员可指定月份，普通用户不指定月份，系统会以当前时间进行匹配',
  })
  async findActiveRecords(
    @Param('enterpriseCode') enterpriseCode: string,
    @Query() query: { semester: string; month: number }
  ) {
    if ((query.semester && !query.month) || (!query.semester && query.month)) {
      throw new Error('必须同时指定学期和月份');
    }
    const list = await this.publicationRecordService.findActiveRecords(
      enterpriseCode,
      query.semester,
      query.month
    );
    return { list };
  }
}
