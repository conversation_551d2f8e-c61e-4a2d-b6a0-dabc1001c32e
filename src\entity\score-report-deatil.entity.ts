import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { IScoreReportDetail } from './interface';
import { ScoreReport } from './score-report.entity';

@Table({
  tableName: 'score_report_detail',
  comment: '成绩汇总报表详情',
  timestamps: false,
})
export class ScoreReportDetail
  extends Model<ScoreReportDetail>
  implements IScoreReportDetail
{
  /** 报表记录ID - 系统自动生成 */
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    field: 'detail_id',
  })
  detailId: number;

  @ForeignKey(() => ScoreReport)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_report_scoring',
      msg: '已存在相同的报表记录',
    },
    field: 'report_id',
    comment: '主报表记录ID',
  })
  reportId: number;

  /** 评分组ID，报表只做软件关联 */
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_report_scoring',
      msg: '已存在相同的报表记录',
    },
    field: 'scoring_id',
    comment: '评分组ID',
  })
  scoringId: number;

  /** 评分组名称 */
  @Column({
    type: DataType.STRING,
    allowNull: false,
    field: 'scoring_name',
    comment: '评分组名称',
  })
  scoringName: string;

  /** 是否是问卷类型 */
  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    field: 'is_question',
    comment: '是否是问卷类型',
  })
  isQuestion: boolean;

  /** 权重 */
  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    field: 'weight',
    comment: '权重',
  })
  weight: number;

  /** 评分 */
  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: false,
    field: 'score',
    comment: '评分',
  })
  score: number;

  @BelongsTo(() => ScoreReport, { onDelete: 'CASCADE' })
  scoreReport: ScoreReport;
}
