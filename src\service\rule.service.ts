import { BaseService } from '../common/BaseService';
import { ModelCtor } from 'sequelize-typescript';
import { CustomError } from '../error/custom.error';
import { Inject, Provide } from '@midwayjs/core';
import { Rule } from '../entity/rule.entity';
import { AssessmentPlan } from '../entity/assessment-plan.entity';
import { Scoring } from '../entity/scoring.entity';
import { ScoringService } from './scoring.service';

/**
 * 规则服务
 */
@Provide()
export class RuleService extends BaseService<Rule> {
  @Inject()
  scoringService: ScoringService;

  constructor() {
    super('规则');
  }

  /**
   * 获取模型
   */
  protected getModel(): ModelCtor<Rule> {
    return Rule;
  }

  /**
   * 创建规则
   * @param rule 规则信息
   */
  async createRule(rule: Partial<Rule>): Promise<Rule> {
    // 验证规则有效性
    await this.validateRule(rule);
    return this.create(rule);
  }

  /**
   * 根据方案ID获取规则列表
   * @param planId 方案ID
   */
  async findByPlanId(planId: number): Promise<Rule[]> {
    try {
      const { list } = await this.findAll({
        query: { planId },
        include: [
          {
            association: 'scorings',
          },
        ],
      });
      return list;
    } catch (error) {
      this.logger.error(
        {
          message: '获取规则列表失败',
          source: '业务',
          operation: '查询',
          data: {
            planId,
          },
        },
        error
      );
      throw new CustomError('获取规则列表失败', 500);
    }
  }

  /**
   * 检查方案配置完整性
   * @param ruleId 赋分规则ID
   * @returns boolean
   */
  async checkConfiguration(ruleId: number): Promise<boolean> {
    try {
      const rule = await this.findById(ruleId);
      if (!rule) {
        throw new CustomError('考核规则不存在', 404);
      }
      // 检查是否存在未配置的观测点
      const scorings = await Scoring.findAll({
        where: { ruleId },
      });
      const flags = await Promise.all(
        scorings.map(async scoring => {
          return await this.scoringService.checkConfiguration(
            scoring.scoringId
          );
        })
      );
      return scorings.length > 0 && flags.every(flag => flag);
    } catch (error) {
      this.logger.error(
        {
          message: `检查方案配置完整性失败: ${error.message}`,
          source: '数据库',
          operation: 'checkRuleConfiguration',
          data: { ruleId },
        },
        error
      );
      throw new CustomError(`检查方案配置完整性失败: ${error.message}`);
    }
  }

  /**
   * 验证规则有效性
   * @param rule 规则信息
   */
  async validateRule(rule: Partial<Rule>): Promise<void> {
    // 验证方案是否存在
    if (rule.planId) {
      const plan = await AssessmentPlan.findByPk(rule.planId);
      if (!plan) {
        throw new CustomError('方案不存在', 404);
      }

      // 验证方案状态
      if (plan.status === 'published') {
        throw new CustomError('已发布的方案不允许修改', 403);
      }
    }

    // 验证被评估类型
    if (
      rule.assessedType === 'role' &&
      (!rule.roles || rule.roles.length === 0)
    ) {
      throw new CustomError('角色类型的规则必须指定角色名称', 400);
    }

    if (
      rule.assessedType === 'user' &&
      (!rule.users || rule.users.length === 0)
    ) {
      throw new CustomError('用户类型的规则必须指定用户ID', 400);
    }
  }
}
