import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { AssessmentPlan } from './assessment-plan.entity';
import { IRule } from './interface';
import { Scoring } from './scoring.entity';

@Table({
  tableName: 'rule',
  comment: '考核规则表',
  timestamps: true,
})
export class Rule extends Model<Rule> implements IRule {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '规则唯一标识',
  })
  ruleId: number;

  @ForeignKey(() => AssessmentPlan)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_plan_title',
      msg: '已存在相同的考核规则',
    },
    comment: '所属方案ID',
  })
  planId: number;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    unique: {
      name: 'unique_plan_title',
      msg: '已存在相同的考核规则',
    },
    comment: '规则名称',
  })
  title: string;

  @Column({
    type: DataType.ENUM('role', 'user'),
    allowNull: false,
    comment: '被评估类型（角色/用户）',
  })
  assessedType: 'role' | 'user';

  @Column({
    type: DataType.JSON,
    allowNull: true,
    comment: '被评估角色列表，每个角色格式为{roleCode: 1, roleName: "管理员"}',
  })
  roles?: {
    roleCode: string;
    roleName: string;
  }[];

  @Column({
    type: DataType.JSON,
    allowNull: true,
    comment: '被评估用户列表，每个用户格式为{userCode: 1, userName: "admin"}',
  })
  users?: {
    userCode: string;
    userName: string;
  }[];

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '备注',
  })
  description?: string;

  @BelongsTo(() => AssessmentPlan)
  assessmentPlan: AssessmentPlan;

  @HasMany(() => Scoring)
  scorings: Scoring[];
}
