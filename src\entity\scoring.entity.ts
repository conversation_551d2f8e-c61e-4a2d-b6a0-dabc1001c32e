import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Rule } from './rule.entity';
import { IScoring } from './interface';
import { ObservationPoint } from './observation-point.entity';

@Table({
  tableName: 'scoring',
  comment: '赋分规则表',
  timestamps: true,
})
export class Scoring extends Model<Scoring> implements IScoring {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '赋分组唯一标识',
  })
  scoringId: number;

  @ForeignKey(() => Rule)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_rule_title',
      msg: '已存在相同的打分规则',
    },
    comment: '所属规则ID',
  })
  ruleId: number;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    unique: {
      name: 'unique_rule_title',
      msg: '已存在相同的打分规则',
    },
    comment: '赋分组名称',
  })
  title: string;

  @Column({
    type: DataType.ENUM('role', 'user', 'mixed', 'question'),
    allowNull: false,
    comment: '评估者类型（角色/用户/混合）',
  })
  assessorType: 'role' | 'user' | 'mixed' | 'question';

  @Column({
    type: DataType.JSON,
    allowNull: true,
    comment: '评估角色列表，每个角色格式为{roleCode: 1, roleName: "管理员"}',
  })
  roles?: {
    roleCode: string;
    roleName: string;
  }[];

  @Column({
    type: DataType.JSON,
    allowNull: true,
    comment: '评估用户列表，每个用户格式为{userCode: 1, userName: "admin"}',
  })
  users?: {
    userCode: string;
    userName: string;
  }[];

  @Column({
    type: DataType.DECIMAL(3, 0),
    allowNull: false,
    comment: '权重系数',
  })
  weight: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '备注',
  })
  description?: string;

  @BelongsTo(() => Rule)
  rule: Rule;

  @HasMany(() => ObservationPoint)
  observationPoints: ObservationPoint[];
}
