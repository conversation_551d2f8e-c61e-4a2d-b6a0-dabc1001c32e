import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  HasMany,
  Model,
  Table,
} from 'sequelize-typescript';
import { IScoreReport } from './interface';
import { AssessmentPlan } from './assessment-plan.entity';
import { ScoreReportDetail } from './score-report-deatil.entity';

@Table({
  tableName: 'score_report',
  comment: '成绩汇总报表',
  timestamps: true,
})
export class ScoreReport extends Model<ScoreReport> implements IScoreReport {
  /** 报表记录ID - 系统自动生成 */
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '报表记录ID - 系统自动生成',
  })
  reportId: number;

  /** 学校编号 */
  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '学校编号',
  })
  enterpriseCode: string;

  /** 学年学期编号 */
  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '学年学期编号',
  })
  semester: string;

  /** 学年学期名称 */
  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '学年学期名称',
  })
  semesterName: string;

  /** 考核方案ID */
  @ForeignKey(() => AssessmentPlan)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '考核方案ID',
  })
  planId: number;

  /** 考核方案名称 */
  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '考核方案名称',
  })
  planName: string;

  /** 考核规则ID，报表只做软件关联 */
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_rule_month_assessed',
      msg: '同一个月同一个规则同一个被考核人只能生成一次报表',
    },
    comment: '考核规则ID',
  })
  ruleId: number;

  /** 考核规则名称 */
  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '考核规则名称',
  })
  ruleName: string;

  /** 被考核人编号 */
  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    unique: {
      name: 'unique_rule_month_assessed',
      msg: '同一个月同一个规则同一个被考核人只能生成一次报表',
    },
    comment: '被考核人编号',
  })
  assessedCode: string;

  /** 被考核人姓名 */
  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '被考核人姓名',
  })
  assessedName: string;

  /** 考核月份 */
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_rule_month_assessed',
      msg: '同一个月同一个规则同一个被考核人只能生成一次报表',
    },
    comment: '考核月份',
  })
  month: number;

  /** 评分 */
  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: false,
    comment: '评分',
  })
  score: number;

  @BelongsTo(() => AssessmentPlan)
  assessmentPlan: AssessmentPlan;

  @HasMany(() => ScoreReportDetail)
  scoreReportDetail: ScoreReportDetail[];
}
