# 教师月度考评系统 API 接口文档

## 基础信息

- **服务地址**: `http://localhost:3140`
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

所有接口都遵循统一的响应格式：

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体的响应数据
  }
}
```

- `errCode`: 错误码，0表示成功，非0表示失败
- `msg`: 响应消息
- `data`: 响应数据

## 考核方案相关接口

### 1. 查询考核方案列表

**接口**: `GET /assessment-plans`

**参数**:
- `enterpriseCode` (必填): 学校编码
- `semester` (必填): 学期
- `current`: 当前页码
- `pageSize`: 每页大小
- `planName`: 方案名称（支持模糊查询）

**响应**:
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "total": 10,
    "list": [
      {
        "planId": 1,
        "planName": "2024年上学期教师考核方案",
        "semester": "2024202501",
        "status": "published"
      }
    ]
  }
}
```

### 2. 创建考核方案

**接口**: `POST /assessment-plans`

**请求体**:
```json
{
  "planName": "2024年下学期教师考核方案",
  "enterpriseCode": "SCHOOL001",
  "semester": "2024202502",
  "semesterName": "2024年下学期",
  "startMonth": "2024-07-01T00:00:00.000Z",
  "endMonth": "2024-12-31T00:00:00.000Z",
  "fillableDates": [],
  "scoringType": "score",
  "maxAdjustment": 10,
  "minAdjustment": -10,
  "publicationType": "score",
  "description": "方案描述"
}
```

**响应**:
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "planId": 123
  }
}
```

### 3. 获取方案详情

**接口**: `GET /assessment-plans/:planId`

**响应**:
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "planId": 1,
    "planName": "2024年上学期教师考核方案",
    "enterpriseCode": "SCHOOL001",
    "semester": "2024202501",
    "status": "published",
    "scoringType": "score",
    "publicationType": "score"
  }
}
```

### 4. 更新考核方案

**接口**: `PUT /assessment-plans/:planId`

**请求体**: 与创建方案相同，但所有字段都是可选的

### 5. 删除考核方案

**接口**: `DELETE /assessment-plans/:planId`

**响应**:
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

### 6. 发布/撤销方案

**接口**: `PATCH /assessment-plans/:planId/status`

**请求体**:
```json
{
  "status": "published"
}
```

### 7. 检查方案配置完整性

**接口**: `GET /assessment-plans/:planId/check`

**响应**:
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

### 8. 复制考核方案 ⭐ 新增

**接口**: `POST /assessment-plans/:planId/copy`

**请求体**:
```json
{
  "planName": "2024年下学期教师考核方案",
  "semester": "2024202502",
  "semesterName": "2024年下学期",
  "startMonth": "2024-07-01T00:00:00.000Z",
  "endMonth": "2024-12-31T00:00:00.000Z",
  "description": "基于上学期方案复制的新方案"
}
```

**响应**:
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "planId": 124,
    "planName": "2024年下学期教师考核方案",
    "message": "方案复制成功"
  }
}
```

**特性**:
- 深度复制所有关联数据（规则、赋分规则、观测点）
- 自动处理唯一约束冲突
- 确保数据隔离性
- 复制的方案状态强制为草稿

## 考核规则相关接口

### 1. 创建规则

**接口**: `POST /assessment-plans/:planId/rule`

### 2. 获取方案下的规则列表

**接口**: `GET /assessment-plans/:planId/rule`

### 3. 更新规则

**接口**: `PUT /assessment-plans/:planId/rule/:ruleId`

### 4. 删除规则

**接口**: `DELETE /assessment-plans/:planId/rule/:ruleId`

## 赋分规则相关接口

### 1. 创建赋分规则

**接口**: `POST /rule/:ruleId/scoring`

### 2. 获取规则下的赋分规则列表

**接口**: `GET /rule/:ruleId/scoring`

### 3. 更新赋分规则

**接口**: `PUT /rule/:ruleId/scoring/:scoringId`

### 4. 删除赋分规则

**接口**: `DELETE /rule/:ruleId/scoring/:scoringId`

## 观测点相关接口

### 1. 批量维护观测点

**接口**: `POST /observation-point/:scoringId/batch`

### 2. 获取赋分规则下的观测点列表

**接口**: `GET /observation-point/:scoringId`

### 3. 导入观测点

**接口**: `POST /observation-point/:scoringId/import`

## 考核任务相关接口

### 1. 获取月度考核分组

**接口**: `GET /assessment-task/groups/:enterpriseCode/:semester/:month`

### 2. 获取月度考核任务列表

**接口**: `GET /assessment-task/monthly/:enterpriseCode/:semester/:month`

### 3. 手动生成月度任务（开发工具）

**接口**: `PUT /dev-tools/generate-monthly-tasks/:planId?month=:month`

## 评分相关接口

### 1. 根据任务ID获取评分列表

**接口**: `GET /score/tasks/:taskId`

### 2. 提交评分

**接口**: `PATCH /score/:taskId/:scoreId`

## 成绩报表相关接口

### 1. 获取报表分组

**接口**: `GET /score-report/groups/:enterpriseCode/:semester/:month`

### 2. 查询成绩报告

**接口**: `GET /score-report/list/:enterpriseCode/:semester/:month/:ruleId`

### 3. 导出Excel报表

**接口**: `GET /score-report/export/*`

## 公示记录相关接口

### 1. 发布公示记录

**接口**: `POST /publication/pub/:enterpriseCode/:semester/:month`

### 2. 撤销公示记录

**接口**: `DELETE /publication/unpub/:enterpriseCode/:semester/:month`

## 问卷集成接口

### 1. 获取学校问卷列表

**接口**: `GET /questionnaire/:schoolCode`

### 2. 获取教师问卷成绩

**接口**: `GET /questionnaire/:questionnaireId/teacher-scores`

## 学校信息接口

### 1. 获取学期列表

**接口**: `GET /schoolInfo/:enterpriseCode/semester`

### 2. 获取角色列表

**接口**: `GET /schoolInfo/:enterpriseCode/schoolRoles`

### 3. 获取用户列表

**接口**: `GET /schoolInfo/:enterpriseCode/users`

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 422 | 参数验证失败 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有时间字段都使用ISO 8601格式
2. 分页查询支持`current`和`pageSize`参数
3. 部分接口需要特定的权限才能访问
4. 开发工具接口仅在本地和测试环境可用
5. 复制方案功能会深度复制所有关联数据，请谨慎使用
