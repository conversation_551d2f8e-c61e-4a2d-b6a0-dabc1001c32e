import {
  Inject,
  Controller,
  Get,
  Put,
  Del,
  Body,
  Param,
  Query,
} from '@midwayjs/core';
import { AssessmentTaskService } from '../service/assessment-task.service';
import { CustomError } from '../error/custom.error';
import { UpdateAssessmentTaskDTO } from '../dto/assessment-task.dto';

/**
 * 考核任务控制器，考核任务只能由系统自动创建，用户通过操作可以更新
 */
@Controller('/assessment-task')
export class AssessmentTaskController {
  @Inject()
  assessmentTaskService: AssessmentTaskService;

  @Get('/groups/:enterpriseCode/:semester/:month', {
    summary: '获取月度考核分组',
  })
  async getGroups(
    @Param('enterpriseCode') enterpriseCode: string,
    @Param('semester') semester: string,
    @Param('month') month: number
  ) {
    const list = await this.assessmentTaskService.getGroups(
      enterpriseCode,
      semester,
      month
    );
    return { list };
  }

  @Get('/analysis/group/:enterpriseCode/:semester/:month/:ruleId', {
    summary: '按考核规则获取分析数据',
  })
  async getAnalysisDataByGroup(
    @Param('enterpriseCode') enterpriseCode: string,
    @Param('semester') semester: string,
    @Param('month') month: number,
    @Param('ruleId') ruleId: number
  ) {
    return this.assessmentTaskService.getAnalysisDataByGroup(
      enterpriseCode,
      semester,
      month,
      ruleId
    );
  }

  /**
   * 根据考核人ID获取任务列表
   * @param assessorUserid 考核人ID
   */
  @Get('/:assessorUserid')
  async getTasksByAssessor(@Param('assessorUserid') assessorUserid: string) {
    try {
      const result = await this.assessmentTaskService.findByAssessor(
        assessorUserid
      );
      return result;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(`获取考核人任务列表失败: ${error.message}`, 500);
    }
  }

  /**
   * 根据被考核人ID获取任务列表
   * @param assessedUserid 被考核人ID
   */
  @Get('/assessed/assessed/:assessedUserid')
  async getTasksByAssessed(@Param('assessedUserid') assessedUserid: string) {
    try {
      const result = await this.assessmentTaskService.findByAssessed(
        assessedUserid
      );
      return result;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(`获取被考核人任务列表失败: ${error.message}`, 500);
    }
  }

  /**
   * 获取任务详情
   * @param taskId 任务ID
   */
  @Get('/:taskId')
  async getTaskDetail(@Param('taskId') taskId: number) {
    try {
      const result = await this.assessmentTaskService.findTaskDetail(taskId);
      return result;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(`获取考核任务详情失败: ${error.message}`, 500);
    }
  }

  /**
   * 更新考核任务
   * @param taskId 任务ID
   * @param task 任务信息
   */
  @Put('/:taskId')
  async updateTask(
    @Param('taskId') taskId: number,
    @Body() task: UpdateAssessmentTaskDTO
  ) {
    try {
      // 验证任务有效性
      await this.assessmentTaskService.validateTask(task);

      // 更新任务
      await this.assessmentTaskService.update({ taskId }, task);
      return true;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(`更新考核任务失败: ${error.message}`, 500);
    }
  }

  /**
   * 删除考核任务
   * @param taskId 任务ID
   */
  @Del('/:taskId')
  async deleteTask(@Param('taskId') taskId: number) {
    try {
      await this.assessmentTaskService.delete({ taskId });
      return true;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(`删除考核任务失败: ${error.message}`, 500);
    }
  }

  @Get('/month/list/:enterpriseCode/:semester', {
    summary: '按学期获取考核任务月度汇总列表',
  })
  async getMonthListBySemester(
    @Param('enterpriseCode') enterpriseCode: string,
    @Param('semester') semester: string,
    @Query() query: Record<string, any>
  ) {
    const list = await this.assessmentTaskService.getMonthListBySemester(
      enterpriseCode,
      semester,
      query
    );
    return { list };
  }

  /**
   * 获取月度考核任务
   * @param planId 方案ID
   * @param month 月份（格式：YYYY-MM）
   */
  @Get('/monthly/:enterpriseCode/:semester/:month', {
    summary: '获取月度考核任务列表',
  })
  async getMonthlyTasks(
    @Param('enterpriseCode') enterpriseCode: string,
    @Param('semester') semester: string,
    @Param('month') month: number,
    @Query() query: Record<string, any>
  ) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.assessmentTaskService.getMonthlyTasks(
      enterpriseCode,
      semester,
      month,
      queryInfo,
      offset,
      limit
    );
  }

  @Get('/monthlyProgress/:enterpriseCode/:semester/:month', {
    summary: '获取月度考核任务进度',
  })
  async getMonthlyTaskProgress(
    @Param('enterpriseCode') enterpriseCode: string,
    @Param('semester') semester: string,
    @Param('month') month: number,
    @Query() query: { assessorName?: string; ruleName?: string }
  ) {
    const list = await this.assessmentTaskService.getMonthlyTaskProgress(
      enterpriseCode,
      semester,
      month,
      query
    );
    return { list };
  }
}
