# 更新日志

## [1.1.1] - 2024-08-11

### 优化改进

#### 🔧 复制方案名称生成逻辑优化

- **智能冲突检测**: 优化了方案名称生成逻辑，只有在真正冲突时才添加"(复制)"后缀
- **逻辑改进**:
  - 原逻辑：无条件添加"(复制)"后缀
  - 新逻辑：先检查是否冲突，不冲突则保持原名称
- **用户体验提升**: 复制到不同学期时可以保持原方案名称，避免不必要的后缀

#### 🐛 问题修复

- **关联查询问题**: 修复了 "Alias cannot be inferred" 错误
  - 删除了重复的关联关系定义
  - 优化了复杂嵌套查询为分离查询
  - 提高了查询的稳定性和性能

### 测试验证

- ✅ 不同学期复制保持原名称
- ✅ 相同学期复制添加"(复制)"后缀
- ✅ 多次复制自动添加数字序号
- ✅ 深度复制功能正常工作

## [1.1.0] - 2024-08-08

### 新增功能

#### 🎯 复制考核方案功能

- **新增接口**: `POST /assessment-plans/:planId/copy` - 复制考核方案
- **深度复制**: 支持复制方案及其所有关联数据
  - 考核规则 (Rule)
  - 赋分规则 (Scoring)
  - 观测点 (ObservationPoint)
- **智能命名**: 自动处理唯一约束冲突
  - 自动添加"(复制)"后缀
  - 冲突时自动添加数字序号
- **数据隔离**: 确保复制的数据完全独立
  - 所有关联数据都创建新记录
  - 复制的方案状态强制为草稿
  - 不与原方案共享任何引用

#### 📝 新增DTO

- `CopyAssessmentPlanDTO`: 复制方案请求参数定义
  - `planName`: 新方案名称（可选）
  - `semester`: 目标学期（可选）
  - `semesterName`: 目标学期名称（可选）
  - `startMonth`: 开始月份（可选）
  - `endMonth`: 结束月份（可选）
  - `description`: 方案描述（可选）

#### 🔧 新增服务方法

- `AssessmentPlanService.copyPlan()`: 主复制方法
- `AssessmentPlanService.generateUniquePlanName()`: 生成唯一名称
- `AssessmentPlanService.copyRulesWithAssociations()`: 复制规则及关联
- `AssessmentPlanService.copyScoringWithObservationPoints()`: 复制赋分规则及观测点
- `AssessmentPlanService.copyObservationPoints()`: 复制观测点

#### 📚 新增文档

- `docs/copy-assessment-plan.md`: 复制方案功能详细说明
- `docs/api-reference.md`: 完整API接口文档
- `docs/CHANGELOG.md`: 更新日志

#### 🧪 新增测试

- `src/test/copy-assessment-plan.test.ts`: 复制功能测试用例
  - 基本复制逻辑测试
  - 名称冲突处理测试
  - 数据结构验证测试

### 技术特性

#### 🔒 事务安全

- 使用数据库事务确保数据一致性
- 复制失败时自动回滚
- 完善的错误处理机制

#### 📊 性能优化

- 批量创建关联数据
- 减少数据库查询次数
- 支持大型方案的复制

#### 🛡️ 数据验证

- 源方案存在性验证
- 参数有效性验证
- 唯一约束检查

### 使用场景

1. **学期方案复制**: 将上学期的考核方案复制到新学期
2. **方案模板**: 基于成熟方案创建新的考核方案
3. **快速配置**: 避免重复配置相似的考核规则和观测点

### API 示例

```bash
# 复制方案
POST /assessment-plans/123/copy
Content-Type: application/json

{
  "planName": "2024年下学期教师考核方案",
  "semester": "2024202502",
  "semesterName": "2024年下学期",
  "startMonth": "2024-07-01T00:00:00.000Z",
  "endMonth": "2024-12-31T00:00:00.000Z",
  "description": "基于上学期方案复制的新方案"
}
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "planId": 124,
    "planName": "2024年下学期教师考核方案",
    "message": "方案复制成功"
  }
}
```

### 注意事项

1. 复制的方案状态始终为草稿，需要手动发布
2. 复制过程可能需要较长时间，特别是对于大型方案
3. 确保有足够的数据库权限进行事务操作
4. 建议在非高峰期进行大型方案的复制操作

### 兼容性

- 向后兼容，不影响现有功能
- 新增接口遵循现有API设计规范
- 支持所有现有的方案配置选项

---

## [1.0.0] - 2024-07-01

### 初始版本

- 考核方案管理
- 考核任务生成
- 评分系统
- 成绩报表
- 问卷集成
- 公示记录管理
