import { MidwayConfig } from '@midwayjs/core';

export default {
  sequelize: {
    dataSource: {
      default: {
        dialect: 'mysql',
        host: '*************',
        // host: '127.0.0.1',
        port: 3306,
        username: 'root',
        password: 'Clouddeep@8890',
        database: 'teacher-evaluation',
        // 模型设置
        define: {
          timestamps: true,
          createdAt: 'createdAt',
          updatedAt: 'updatedAt',
          // 禁用驼峰转换
          underscored: false,
          charset: 'utf8mb4',
          collate: 'utf8mb4_bin',
        },
        // 时区设置
        timezone: '+08:00',
        // 连接池配置
        pool: {
          max: 5,
          min: 0,
          idle: 10000,
        },
        entities: ['entity'],
      },
    },
  },
  // 日志配置
  midwayLogger: {
    default: {
      // 日志输出级别
      level: 'info',
      // 日志文件配置
      transports: {
        file: {
          maxFiles: '7d',
        },
        error: {
          maxFiles: '7d',
        },
        console: false,
        json: {
          fileLogName: 'teacher-evaluation.json.log',
        },
      },
    },
  },
  // 请求日志中间件配置
  requestLogger: {
    // 排除不需要记录日志的路径
    // excludePaths: ['/health', '/favicon.ico', '/metrics*'],
    // 是否记录请求体
    logRequestBody: true,
    // 是否记录响应体（可能会导致日志过大，建议仅在开发环境启用）
    logResponseBody: true,
  },
} as MidwayConfig;
