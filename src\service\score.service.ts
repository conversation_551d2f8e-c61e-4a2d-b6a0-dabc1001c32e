import { BaseService } from '../common/BaseService';
import { Score } from '../entity/score.entity';
import { ModelCtor } from 'sequelize-typescript';
import { CustomError } from '../error/custom.error';
import { Provide } from '@midwayjs/core';
import { AssessmentTask } from '../entity/assessment-task.entity';
import { ObservationPoint } from '../entity/observation-point.entity';
import { Op } from 'sequelize';
import { AssessmentPlan } from '../entity/assessment-plan.entity';
import { PublicationRecord } from '../entity/publication-record.entity';

/**
 * 评分服务
 */
@Provide()
export class ScoreService extends BaseService<Score> {
  constructor() {
    super('评分');
  }

  /**
   * 获取模型
   */
  protected getModel(): ModelCtor<Score> {
    return Score;
  }

  /**
   * 根据任务ID获取评分列表
   * @param taskId 任务ID
   */
  async findByTaskId(
    taskId: number
  ): Promise<{ plan: AssessmentPlan; list: Score[] }> {
    const task = await AssessmentTask.findOne({
      where: { taskId },
      include: AssessmentPlan,
    });
    const list = await Score.findAll({
      where: { taskId },
      include: [
        {
          model: ObservationPoint,
        },
      ],
    });
    return { plan: task.assessmentPlan, list };
  }

  /**
   * 查询被考核人的历史评分记录
   * @param assessedId 被考核人ID
   * @param startDate 开始日期（可选）
   * @param endDate 结束日期（可选）
   */
  async findScoreHistoryByAssessed(
    assessedId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    try {
      // 构建查询条件
      const whereCondition: any = {};
      if (startDate && endDate) {
        whereCondition.createdAt = {
          [Op.between]: [startDate, endDate],
        };
      } else if (startDate) {
        whereCondition.createdAt = {
          [Op.gte]: startDate,
        };
      } else if (endDate) {
        whereCondition.createdAt = {
          [Op.lte]: endDate,
        };
      }

      // 查询该被考核人的所有任务
      const tasks = await AssessmentTask.findAll({
        where: {
          assessedId,
          ...whereCondition,
        },
        include: [
          {
            model: AssessmentPlan,
            as: 'assessmentPlan',
          },
          {
            model: Score,
            as: 'scores',
            include: [
              {
                model: ObservationPoint,
                as: 'observationPoint',
              },
            ],
          },
        ],
      });

      // 格式化结果
      const result = tasks.map(task => {
        const totalScore = task.scores.reduce(
          (sum, score) => sum + Number(score.scoreValue),
          0
        );

        return {
          taskId: task.taskId,
          planId: task.planId,
          planName: task.assessmentPlan?.planName,
          month: task.month,
          assessorCode: task.assessorCode,
          assessorName: task.assessorName,
          status: task.status,
          totalScore,
          scores: task.scores.map(score => ({
            scoreId: score.scoreId,
            pointId: score.pointId,
            pointName: score.observationPoint?.pointName,
            scoreValue: score.scoreValue,
            createdAt: score.createdAt,
          })),
        };
      });

      return result;
    } catch (error) {
      this.logger.error(
        {
          message: `查询被考核人历史评分记录失败: ${error.message}`,
          source: '数据库',
          operation: 'findScoreHistoryByAssessed',
          data: { assessedId, startDate, endDate },
        },
        error
      );
      throw new CustomError(`查询被考核人历史评分记录失败: ${error.message}`);
    }
  }

  /**
   * 验证评分有效性，提交前验证
   * @param score 评分信息
   */
  public async validateScore(score: Partial<Score>): Promise<void> {
    if (!score.scoreId) {
      throw new CustomError('评分ID不能为空');
    }
    const scoreInfo = await Score.findByPk(score.scoreId);
    if (!scoreInfo) {
      throw new CustomError('未找到指定评分信息', 404);
    }

    if (score.scoreValue === undefined || score.scoreValue === null) {
      throw new CustomError('评分值不能为空');
    }

    // 验证任务是否存在
    const task = await AssessmentTask.findOne({
      where: { taskId: scoreInfo.taskId },
      include: AssessmentPlan,
    });
    if (!task) {
      throw new CustomError('指定的考核任务不存在');
    }

    // 验证观测点是否存在
    const point = await ObservationPoint.findByPk(scoreInfo.pointId);
    if (!point) {
      throw new CustomError('指定的观测点不存在');
    }

    // 验证任务是否已完成
    if (task.status === 'completed') {
      throw new CustomError('该任务已完成，无法进行评分');
    }

    // 验证是否已公示
    const isPub = await PublicationRecord.findOne({
      where: { planId: task.planId, month: task.month },
    });
    if (isPub) {
      throw new CustomError('该任务已公示，不再允许进行评分');
    }

    // 验证评分日期是否在考核计划的可评分日期范围内
    const data = new Date(scoreInfo.createdAt);
    const year = data.getFullYear();
    const month = String(data.getMonth() + 1).padStart(2, '0');
    if (task.assessmentPlan.fillableDates.length) {
      // 指定填写日期时按日期进行判断，否则代表整月都能填写
      const fillableDates = task.assessmentPlan.fillableDates
        .map(d => {
          const day = String(d).padStart(2, '0');
          const str = `${year}-${month}-${day}`;
          const date = new Date(str);
          // 判断日期是否合法
          const isValidDate =
            !isNaN(date.getTime()) && date.getMonth() + 1 === Number(month);
          return isValidDate ? date : null;
        })
        .filter(i => !!i);

      const today = new Date();
      const isToday = fillableDates.some(
        date =>
          date.getFullYear() === today.getFullYear() &&
          date.getMonth() === today.getMonth() &&
          date.getDate() === today.getDate()
      );
      if (!isToday) {
        throw new CustomError('评分日期不在考核计划的可评分日期范围内');
      }
    }
  }
}
