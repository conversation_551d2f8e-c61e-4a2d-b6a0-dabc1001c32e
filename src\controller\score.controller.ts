import {
  Body,
  Controller,
  Get,
  Inject,
  Param,
  Query,
  Patch,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ScoreService } from '../service/score.service';
import { AssessmentTaskService } from '../service/assessment-task.service';
import { CustomError } from '../error/custom.error';
import { UpdateScoreDTO } from '../dto/score.dto';

/**
 * 评分控制器
 */
@Controller('/score')
export class ScoreController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ScoreService;

  @Inject()
  assessmentTaskService: AssessmentTaskService;

  /**
   * 根据任务ID获取评分列表
   * @param taskId 任务ID
   */
  @Get('/tasks/:taskId', { summary: '根据任务ID获取评分列表' })
  async getScoresByTaskId(@Param('taskId') taskId: number) {
    try {
      const result = await this.service.findByTaskId(taskId);
      return result;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(`获取任务评分列表失败: ${error.message}`, 500);
    }
  }

  /**
   * 查询评分
   * @param scoreId 评分ID
   */
  @Get('/:scoreId', { summary: '查询评分' })
  async getScore(@Param('scoreId') scoreId: string) {
    try {
      const result = await this.service.findById(scoreId);
      if (!result) {
        throw new CustomError('未找到指定评分信息', 404);
      }
      return result;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(`查询评分失败: ${error.message}`, 500);
    }
  }

  /**
   * 提交评分
   * @param scoreId 评分ID
   * @param score 评分信息
   */
  @Patch('/:taskId/:scoreId', { summary: '提交评分' })
  async putScore(
    @Param('taskId') taskId: number,
    @Param('scoreId') scoreId: number,
    @Body() value: UpdateScoreDTO
  ) {
    try {
      // 验证评分有效性
      await this.service.validateScore({
        scoreValue: value.scoreValue,
        scoreId,
      });

      // 更新评分
      await this.service.update({ scoreId }, value);

      // 检查任务是否已完成
      const { list: scores } = await this.service.findByTaskId(taskId);
      const isTaskCompleted = scores.every(score => score.scoreValue !== null);
      // 如果任务已完成，更新任务状态
      if (isTaskCompleted) {
        await this.assessmentTaskService.update(
          { taskId },
          { status: 'completed' }
        );
      }
      return true;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(`更新评分失败: ${error.message}`, 500);
    }
  }

  /**
   * 获取被考核人评分历史
   * @param assessedUserid 被考核人ID
   */
  @Get('/history/assessed/:assessedUserid', { summary: '获取被考核人评分历史' })
  async getScoreHistoryByAssessed(
    @Param('assessedUserid') assessedUserid: string,
    @Query() query: any
  ) {
    try {
      const { startDate, endDate } = query;
      const result = await this.service.findScoreHistoryByAssessed(
        assessedUserid,
        startDate,
        endDate
      );
      return result;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(`获取被考核人评分历史失败: ${error.message}`, 500);
    }
  }
}
