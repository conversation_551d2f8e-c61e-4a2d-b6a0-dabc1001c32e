import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 创建赋分规则DTO
 */
export class CreateScoringDTO {
  @Rule(RuleType.string().required().error(new Error('赋分组名称不能为空')))
  title: string;

  @Rule(
    RuleType.string()
      .valid('role', 'user', 'mixed', 'question')
      .required()
      .error(
        new Error('评估者类型必须为角色（role）、用户（user）或混合（mixed）')
      )
  )
  assessorType: 'role' | 'user' | 'mixed' | 'question';

  @Rule(
    RuleType.array()
      .items(
        RuleType.object().keys({
          roleName: RuleType.string().required(),
          roleCode: RuleType.string().required(),
        })
      )
      .optional()
      .error(new Error('角色名称列表格式不正确'))
  )
  roles?: {
    roleCode: string;
    roleName: string;
  }[];

  @Rule(
    RuleType.array()
      .items(
        RuleType.object().keys({
          userCode: RuleType.string().required(),
          userName: RuleType.string().required(),
        })
      )
      .optional()
      .error(new Error('用户ID列表格式不正确'))
  )
  users?: {
    userCode: string;
    userName: string;
  }[];

  @Rule(
    RuleType.number()
      .min(0)
      .max(100)
      .required()
      .error(new Error('权重必须为0-100之间的数值'))
  )
  weight: number;

  @Rule(RuleType.string().optional())
  description?: string;
}

/**
 * 更新赋分规则DTO
 */
export class UpdateScoringDTO {
  @Rule(RuleType.number().optional())
  ruleId?: number;

  @Rule(RuleType.string().optional())
  title?: string;

  @Rule(
    RuleType.string()
      .valid('role', 'user', 'mixed', 'question')
      .optional()
      .error(
        new Error('评估者类型必须为角色（role）、用户（user）或混合（mixed）')
      )
  )
  assessorType?: 'role' | 'user' | 'mixed' | 'question';

  @Rule(
    RuleType.array()
      .items(
        RuleType.object().keys({
          roleCode: RuleType.string().required(),
          roleName: RuleType.string().required(),
        })
      )
      .optional()
      .error(new Error('角色名称列表格式不正确'))
  )
  roles?: {
    roleCode: string;
    roleName: string;
  }[];

  @Rule(
    RuleType.array()
      .items(
        RuleType.object().keys({
          userCode: RuleType.string().required(),
          userName: RuleType.string().required(),
        })
      )
      .optional()
      .error(new Error('用户ID列表格式不正确'))
  )
  users?: {
    userCode: string;
    userName: string;
  }[];

  @Rule(
    RuleType.number()
      .min(0)
      .max(100)
      .optional()
      .error(new Error('权重必须为0-100之间的数值'))
  )
  weight?: number;

  @Rule(RuleType.string().optional())
  description?: string;
}
