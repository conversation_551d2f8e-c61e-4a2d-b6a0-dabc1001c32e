/*
 * @Description: 请求日志中间件
 * @Date: 2025-01-09 08:43:18
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-03-25 11:00:44
 */
import { Config, IMiddleware, Inject, Middleware } from '@midwayjs/core';
import { Context, NextFunction } from '@midwayjs/koa';
import { CustomLogger } from '../common/CustomLogger';

@Middleware()
export class RequestLoggerMiddleware
  implements IMiddleware<Context, NextFunction>
{
  @Inject()
  logger: CustomLogger;

  @Config('requestLogger')
  requestLoggerConfig: {
    excludePaths?: string[];
    logRequestBody?: boolean;
    logResponseBody?: boolean;
  } = {};

  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      // 检查是否需要跳过日志记录（例如健康检查路径）
      if (this.shouldSkipLogging(ctx)) {
        return await next();
      }

      // 使用CustomLogger记录请求
      const logResponse = this.logger.logHttpRequest(ctx);

      try {
        // 执行下一个中间件
        await next();
      } catch (error) {
        // 记录错误
        this.logger.error(
          {
            message: `请求处理错误: ${error.message}`,
            source: '接口',
            details: error.stack,
            data: {
              url: ctx.url,
              method: ctx.method,
            },
          },
          error
        );
        throw error;
      } finally {
        // 记录响应信息
        logResponse();

        // 如果配置了记录响应体且响应体存在
        if (this.requestLoggerConfig.logResponseBody && ctx.body) {
          this.logger.debug({
            message: '响应体内容',
            source: '接口',
            data: {
              responseBody:
                typeof ctx.body === 'object'
                  ? JSON.stringify(ctx.body)
                  : ctx.body,
            },
          });
        }
      }
    };
  }

  /**
   * 判断是否应该跳过日志记录
   *
   * @private
   * @param {Context} ctx Koa上下文
   * @return {boolean} 是否跳过
   * @memberof RequestLoggerMiddleware
   */
  private shouldSkipLogging(ctx: Context): boolean {
    // 如果配置了排除路径
    if (
      this.requestLoggerConfig.excludePaths &&
      this.requestLoggerConfig.excludePaths.length > 0
    ) {
      return this.requestLoggerConfig.excludePaths.some(path => {
        // 支持精确匹配和通配符匹配
        if (path.endsWith('*')) {
          const prefix = path.slice(0, -1);
          return ctx.path.startsWith(prefix);
        }
        return ctx.path === path;
      });
    }
    return false;
  }
}
