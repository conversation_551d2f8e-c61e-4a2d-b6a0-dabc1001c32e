# 复制考核方案功能

## 功能概述

复制考核方案功能允许用户基于现有方案创建新的方案，包括深度复制所有关联的规则、赋分规则和观测点，确保数据隔离性和避免唯一约束冲突。

## API 接口

### 复制方案

**接口地址**: `POST /assessment-plans/{planId}/copy`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| planId | number | 是 | 源方案ID（路径参数） |
| planName | string | 否 | 新方案名称，不填则自动生成 |
| semester | string | 否 | 目标学期，不填则使用源方案学期 |
| semesterName | string | 否 | 目标学期名称 |
| startMonth | Date | 否 | 开始月份 |
| endMonth | Date | 否 | 结束月份 |
| description | string | 否 | 方案描述 |

**请求示例**:

```json
{
  "planName": "2024年下学期教师考核方案",
  "semester": "2024202502",
  "semesterName": "2024年下学期",
  "startMonth": "2024-07-01T00:00:00.000Z",
  "endMonth": "2024-12-31T00:00:00.000Z",
  "description": "基于上学期方案复制的新方案"
}
```

**响应示例**:

```json
{
  "planId": 123,
  "planName": "2024年下学期教师考核方案",
  "message": "方案复制成功"
}
```

## 功能特性

### 1. 智能唯一约束处理

- **智能名称生成**:
  - 首先检查指定的方案名称是否在目标学期中冲突
  - 如果不冲突，直接使用原名称
  - 只有在冲突时才添加"(复制)"后缀
- **冲突解决**: 如果带"(复制)"的名称仍然冲突，会自动添加数字后缀，如"方案名(复制)1"、"方案名(复制)2"
- **约束检查**: 基于 `planName + enterpriseCode + semester` 的唯一约束进行检查

### 2. 数据隔离性

- **深度复制**: 复制方案及其所有关联数据，包括：
  - 考核规则 (Rule)
  - 赋分规则 (Scoring)  
  - 观测点 (ObservationPoint)
- **独立数据**: 所有复制的数据都是新记录，不与原方案共享引用
- **状态重置**: 复制的方案状态强制设为 'draft'（草稿）

### 3. 数据结构

复制过程会保持以下层级结构：

```
新方案 (AssessmentPlan)
├── 新规则1 (Rule)
│   ├── 新赋分规则1 (Scoring)
│   │   ├── 新观测点1 (ObservationPoint)
│   │   └── 新观测点2 (ObservationPoint)
│   └── 新赋分规则2 (Scoring)
│       └── 新观测点3 (ObservationPoint)
└── 新规则2 (Rule)
    └── ...
```

## 使用场景

1. **学期方案复制**: 将上学期的考核方案复制到新学期
2. **方案模板**: 基于成熟方案创建新的考核方案
3. **快速配置**: 避免重复配置相似的考核规则和观测点

## 注意事项

1. **权限检查**: 确保用户有权限访问源方案
2. **数据完整性**: 复制过程使用事务确保数据一致性
3. **性能考虑**: 复制大型方案可能需要较长时间
4. **状态管理**: 复制的方案始终为草稿状态，需要用户手动发布

## 错误处理

- **404**: 源方案不存在
- **400**: 参数验证失败
- **500**: 复制过程中发生错误（会自动回滚）

## 示例代码

### 前端调用示例

```javascript
// 复制方案
async function copyAssessmentPlan(sourcePlanId, copyData) {
  try {
    const response = await fetch(`/assessment-plans/${sourcePlanId}/copy`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(copyData)
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('复制成功:', result);
      return result;
    } else {
      throw new Error('复制失败');
    }
  } catch (error) {
    console.error('复制方案失败:', error);
    throw error;
  }
}

// 使用示例
copyAssessmentPlan(456, {
  planName: '新学期考核方案',
  semester: '2024202502',
  semesterName: '2024年下学期'
});
```
