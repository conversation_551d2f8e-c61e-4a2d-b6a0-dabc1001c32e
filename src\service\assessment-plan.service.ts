import { <PERSON><PERSON><PERSON> } from 'sequelize-typescript';
import { Inject, Provide } from '@midwayjs/core';
import { BaseService } from '../common/BaseService';
import { CustomError } from '../error/custom.error';
import { AssessmentPlan } from '../entity/assessment-plan.entity';
import { Rule } from '../entity/rule.entity';
import { Scoring } from '../entity/scoring.entity';
import { ObservationPoint } from '../entity/observation-point.entity';
import { RuleService } from './rule.service';
import { ScoringService } from './scoring.service';
import { ObservationPointService } from './observation-point.service';
import { Op } from 'sequelize';

/**
 * 考核方案服务
 */
@Provide()
export class AssessmentPlanService extends BaseService<AssessmentPlan> {
  @Inject()
  ruleService: RuleService;

  @Inject()
  scoringService: ScoringService;

  @Inject()
  observationPointService: ObservationPointService;

  constructor() {
    super('考核方案');
  }

  /**
   * 获取模型
   */
  protected getModel(): ModelCtor<AssessmentPlan> {
    return AssessmentPlan;
  }

  /**
   * 创建考核方案
   * @param plan 方案信息
   */
  async createPlan(plan: Partial<AssessmentPlan>): Promise<AssessmentPlan> {
    // 验证方案有效性
    await this.validatePlan(plan);
    return this.create(plan);
  }

  /**
   * 根据学期获取考核方案列表
   * @param semester 学期
   */
  async findBySemester(semester: string): Promise<AssessmentPlan[]> {
    try {
      const { list } = await this.findAll({
        query: { semester },
        include: [
          {
            association: 'scoringConfigs',
          },
          {
            association: 'assessmentRules',
          },
        ],
      });
      return list;
    } catch (error) {
      this.logger.error(
        {
          message: `根据学期获取考核方案失败: ${error.message}`,
          source: '数据库',
          operation: 'findBySemester',
          data: { semester },
        },
        error
      );
      throw new CustomError(`根据学期获取考核方案失败: ${error.message}`);
    }
  }

  /**
   * 获取方案详情
   * @param planId 方案ID
   */
  async findPlanDetail(planId: number): Promise<AssessmentPlan> {
    try {
      const plan = await AssessmentPlan.findByPk(planId, {
        include: [
          {
            association: 'scoringConfigs',
          },
          {
            association: 'assessmentRules',
            include: [
              {
                association: 'scoringConfig',
              },
            ],
          },
        ],
      });

      if (!plan) {
        throw new CustomError('指定的考核方案不存在');
      }

      return plan;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      this.logger.error(
        {
          message: `获取方案详情失败: ${error.message}`,
          source: '数据库',
          operation: 'findPlanDetail',
          data: { planId },
        },
        error
      );
      throw new CustomError(`获取方案详情失败: ${error.message}`);
    }
  }

  /**
   * 检查方案配置完整性
   * @param planId 方案ID
   */
  async checkConfiguration(planId: number): Promise<boolean> {
    try {
      const plan = await this.findById(planId);
      if (!plan) {
        throw new CustomError('方案不存在', 404);
      }
      // 检查是否存在未配置的观测点
      const rules = await Rule.findAll({
        where: { planId },
      });
      const flags = await Promise.all(
        rules.map(async rule => {
          return await this.ruleService.checkConfiguration(rule.ruleId);
        })
      );
      return rules.length > 0 && flags.every(flag => flag);
    } catch (error) {
      this.logger.error(
        {
          message: `检查方案配置完整性失败: ${error.message}`,
          source: '数据库',
          operation: 'checkConfiguration',
          data: { planId },
        },
        error
      );
      throw new CustomError(`检查方案配置完整性失败: ${error.message}`);
    }
  }

  /**
   * 检查除自己之外其他已启用的方案与自己是否有时间冲突
   * @param planId 方案ID
   */
  async findConflictPlans(planId: number): Promise<AssessmentPlan[]> {
    const plan = await this.findById(planId);
    if (!plan) {
      throw new CustomError('方案不存在', 404);
    }
    const { list } = await this.findAll({
      query: {
        enterpriseCode: plan.enterpriseCode,
        status: 'published',
        planId: { [Op.not]: planId },
      },
    });
    // 检查是否有时间冲突
    const conflictPlans = list.filter(existingPlan => {
      const existingStart = new Date(existingPlan.startMonth);
      const existingEnd = new Date(existingPlan.endMonth);
      console.log('existingStart', existingStart);
      console.log('existingEnd', existingEnd);
      console.log('plan.startMonth', plan.startMonth);
      console.log('plan.endMonth', plan.endMonth);
      return (
        (plan.startMonth >= existingStart && plan.startMonth <= existingEnd) ||
        (plan.endMonth >= existingStart && plan.endMonth <= existingEnd) ||
        (plan.startMonth <= existingStart && plan.endMonth >= existingEnd)
      );
    });
    return conflictPlans;
  }

  /**
   * 创建或更新前验证方案有效性
   * @param plan 方案信息
   */
  private async validatePlan(plan: Partial<AssessmentPlan>): Promise<void> {
    // 验证开始月份必须早于结束月份
    const startMonth = new Date(plan.startMonth);
    const endMonth = new Date(plan.endMonth);
    if (startMonth > endMonth) {
      throw new CustomError('开始月份必须早于结束月份', 400);
    }

    // 检查时间段冲突，只检查已发布的方案
    const { list: publishedPlans } = await this.findAll({
      query: { enterpriseCode: plan.enterpriseCode, status: 'published' },
    });
    const hasConflict = publishedPlans.some(existingPlan => {
      // 如果是同一个方案的更新，则不检查冲突
      if (plan.planId && existingPlan.planId === plan.planId) {
        return false;
      }
      const existingStart = new Date(existingPlan.startMonth);
      const existingEnd = new Date(existingPlan.endMonth);
      return (
        (startMonth >= existingStart && startMonth <= existingEnd) ||
        (endMonth >= existingStart && endMonth <= existingEnd) ||
        (startMonth <= existingStart && endMonth >= existingEnd)
      );
    });

    if (hasConflict && plan.status === 'published') {
      throw new CustomError('考核时间段冲突，请检查', 400);
    }
  }

  /**
   * 复制考核方案
   * @param sourcePlanId 源方案ID
   * @param copyData 复制时的新数据
   */
  async copyPlan(
    sourcePlanId: number,
    copyData: {
      planName?: string;
      semester?: string;
      semesterName?: string;
      startMonth?: Date;
      endMonth?: Date;
      description?: string;
    }
  ): Promise<AssessmentPlan> {
    // 获取源方案
    const sourcePlan = await AssessmentPlan.findByPk(sourcePlanId);
    if (!sourcePlan) {
      throw new CustomError('源方案不存在', 404);
    }

    // 分别获取关联数据
    const sourceRules = await Rule.findAll({
      where: { planId: sourcePlanId },
      include: [
        {
          model: Scoring,
          include: [
            {
              model: ObservationPoint,
            },
          ],
        },
      ],
    });

    // 开始事务
    const transaction = await AssessmentPlan.sequelize.transaction();

    try {
      // 生成新的方案名称，避免唯一约束冲突
      const newPlanName = await this.generateUniquePlanName(
        copyData.planName || sourcePlan.planName,
        copyData.semester || sourcePlan.semester,
        sourcePlan.enterpriseCode
      );

      // 创建新方案
      const newPlanData = {
        planName: newPlanName,
        enterpriseCode: sourcePlan.enterpriseCode,
        semester: copyData.semester || sourcePlan.semester,
        semesterName: copyData.semesterName || sourcePlan.semesterName,
        startMonth: copyData.startMonth || sourcePlan.startMonth,
        endMonth: copyData.endMonth || sourcePlan.endMonth,
        fillableDates: sourcePlan.fillableDates,
        scoringType: sourcePlan.scoringType,
        maxAdjustment: sourcePlan.maxAdjustment,
        minAdjustment: sourcePlan.minAdjustment,
        publicationType: sourcePlan.publicationType,
        maxStars: sourcePlan.maxStars,
        gradeRules: sourcePlan.gradeRules,
        description: copyData.description || sourcePlan.description,
        status: 'draft' as const, // 复制的方案始终为草稿状态
      };

      const newPlan = await AssessmentPlan.create(newPlanData, { transaction });

      // 复制所有规则及其关联数据
      if (sourceRules && sourceRules.length > 0) {
        await this.copyRulesWithAssociations(
          sourceRules,
          newPlan.planId,
          transaction
        );
      }

      // 提交事务
      await transaction.commit();

      this.logger.info({
        message: '方案复制成功',
        source: '业务',
        operation: 'copyPlan',
        data: {
          sourcePlanId,
          newPlanId: newPlan.planId,
          newPlanName,
        },
      });

      return newPlan;
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      this.logger.error(
        {
          message: `复制方案失败: ${error.message}`,
          source: '数据库',
          operation: 'copyPlan',
          data: { sourcePlanId, copyData },
        },
        error
      );
      throw new CustomError(`复制方案失败: ${error.message}`);
    }
  }

  /**
   * 生成唯一的方案名称，避免唯一约束冲突
   * @param baseName 基础名称
   * @param semester 学期
   * @param enterpriseCode 学校编码
   */
  private async generateUniquePlanName(
    baseName: string,
    semester: string,
    enterpriseCode: string
  ): Promise<string> {
    // 首先检查原名称是否冲突
    const originalExists = await AssessmentPlan.findOne({
      where: {
        planName: baseName,
        semester,
        enterpriseCode,
      },
    });

    // 如果原名称不冲突，直接返回
    if (!originalExists) {
      return baseName;
    }

    // 如果原名称冲突，开始生成带"(复制)"的名称
    let candidateName = baseName.includes('(复制)')
      ? baseName
      : `${baseName}(复制)`;

    // 检查是否存在冲突
    let counter = 1;
    while (true) {
      const existing = await AssessmentPlan.findOne({
        where: {
          planName: candidateName,
          semester,
          enterpriseCode,
        },
      });

      if (!existing) {
        return candidateName;
      }

      // 如果冲突，添加数字后缀
      candidateName = baseName.includes('(复制)')
        ? `${baseName}${counter}`
        : `${baseName}(复制)${counter}`;
      counter++;
    }
  }

  /**
   * 复制规则及其所有关联数据
   * @param sourceRules 源规则列表
   * @param newPlanId 新方案ID
   * @param transaction 事务
   */
  private async copyRulesWithAssociations(
    sourceRules: Rule[],
    newPlanId: number,
    transaction: any
  ): Promise<void> {
    for (const sourceRule of sourceRules) {
      // 创建新规则
      const newRuleData = {
        planId: newPlanId,
        title: sourceRule.title,
        assessedType: sourceRule.assessedType,
        roles: sourceRule.roles,
        users: sourceRule.users,
        description: sourceRule.description,
      };

      const newRule = await Rule.create(newRuleData, { transaction });

      // 复制赋分规则及其观测点
      if (sourceRule.scorings && sourceRule.scorings.length > 0) {
        await this.copyScoringWithObservationPoints(
          sourceRule.scorings,
          newRule.ruleId,
          transaction
        );
      }
    }
  }

  /**
   * 复制赋分规则及其观测点
   * @param sourceScorings 源赋分规则列表
   * @param newRuleId 新规则ID
   * @param transaction 事务
   */
  private async copyScoringWithObservationPoints(
    sourceScorings: Scoring[],
    newRuleId: number,
    transaction: any
  ): Promise<void> {
    for (const sourceScoring of sourceScorings) {
      // 创建新赋分规则
      const newScoringData = {
        ruleId: newRuleId,
        title: sourceScoring.title,
        assessorType: sourceScoring.assessorType,
        roles: sourceScoring.roles,
        users: sourceScoring.users,
        weight: sourceScoring.weight,
        description: sourceScoring.description,
      };

      const newScoring = await Scoring.create(newScoringData, { transaction });

      // 复制观测点
      if (sourceScoring.observationPoints && sourceScoring.observationPoints.length > 0) {
        await this.copyObservationPoints(
          sourceScoring.observationPoints,
          newScoring.scoringId,
          transaction
        );
      }
    }
  }

  /**
   * 复制观测点
   * @param sourcePoints 源观测点列表
   * @param newScoringId 新赋分规则ID
   * @param transaction 事务
   */
  private async copyObservationPoints(
    sourcePoints: ObservationPoint[],
    newScoringId: number,
    transaction: any
  ): Promise<void> {
    for (const sourcePoint of sourcePoints) {
      const newPointData = {
        scoringId: newScoringId,
        pointName: sourcePoint.pointName,
        description: sourcePoint.description,
        baseScore: sourcePoint.baseScore,
        orderIndex: sourcePoint.orderIndex,
      };

      await ObservationPoint.create(newPointData, { transaction });
    }
  }
}
