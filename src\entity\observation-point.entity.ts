import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Scoring } from './scoring.entity';
import { IObservationPoint } from './interface';

@Table({
  tableName: 'observation_point',
  comment: '观测点表',
  timestamps: true,
})
export class ObservationPoint
  extends Model<ObservationPoint>
  implements IObservationPoint
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '观测点唯一标识',
  })
  pointId: number;

  @ForeignKey(() => Scoring)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_scoring_name',
      msg: '已存在相同的观测点',
    },
    comment: '所属赋分组ID',
  })
  scoringId: number;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    unique: {
      name: 'unique_scoring_name',
      msg: '已存在相同的观测点',
    },
    comment: '观测点名称',
  })
  pointName: string;

  @Column({
    type: DataType.STRING(200),
    allowNull: true,
    comment: '评分说明',
  })
  description?: string;

  @Column({
    type: DataType.DECIMAL(4, 1),
    allowNull: false,
    comment: '基础分值',
  })
  baseScore: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '排序索引',
  })
  orderIndex: number;

  @BelongsTo(() => Scoring, { onDelete: 'CASCADE' })
  scoring: Scoring;
}
