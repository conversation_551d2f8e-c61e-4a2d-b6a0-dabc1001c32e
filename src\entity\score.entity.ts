import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { AssessmentTask } from './assessment-task.entity';
import { ObservationPoint } from './observation-point.entity';
import { IScore } from './interface';

@Table({
  tableName: 'score',
  comment: '评分表',
  timestamps: true,
})
export class Score extends Model<Score> implements IScore {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '评分唯一标识',
  })
  scoreId: number;

  @ForeignKey(() => AssessmentTask)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_task_point',
      msg: '不能重复打分',
    },
    comment: '所属任务ID',
  })
  taskId: number;

  @ForeignKey(() => ObservationPoint)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_task_point',
      msg: '不能重复打分',
    },
    comment: '观测点ID',
  })
  pointId: number;

  @Column({
    type: DataType.DECIMAL(4, 1),
    allowNull: true,
    comment: '实际得分（分值直接存储，星级存储换算后分数）',
  })
  scoreValue?: number;

  @Column({
    type: DataType.STRING(200),
    allowNull: true,
    comment: '备注',
  })
  remark?: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
    comment: '附件',
  })
  files?: string[];

  @BelongsTo(() => AssessmentTask)
  assessmentTask: AssessmentTask;

  @BelongsTo(() => ObservationPoint, { onDelete: 'CASCADE' })
  observationPoint: ObservationPoint;
}
