import {
  Body,
  Controller,
  Get,
  Inject,
  Param,
  Patch,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ScoreReportService } from '../service/score-report.service';

@Controller('/score-report')
export class ScoreReportController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ScoreReportService;

  @Get('/groups/:enterpriseCode/:semester/:month', { summary: '获取报表分组' })
  async groups(
    @Param('enterpriseCode') enterpriseCode: string,
    @Param('semester') semester: string,
    @Param('month') month: number
  ) {
    const list = await this.service.getScoreReportGroups(
      enterpriseCode,
      semester,
      month
    );
    return { list };
  }

  @Get('/list/:enterpriseCode/:semester/:month/:ruleId', {
    summary: '按考核分组查询成绩报告',
  })
  async list(
    @Param('enterpriseCode') enterpriseCode: string,
    @Param('semester') semester: string,
    @Param('month') month: number,
    @Param('ruleId') ruleId: number,
    @Query('assessedName') assessedName?: string
  ) {
    const res = await this.service.getScoreReport(
      enterpriseCode,
      semester,
      month,
      ruleId,
      { assessedName }
    );
    return res;
  }

  @Get('/listForQuestion/:enterpriseCode/:semester/:month/:ruleId', {
    summary: '按考核分组查询成绩报告，仅获取问卷相关记录',
  })
  async listForQuestion(
    @Param('enterpriseCode') enterpriseCode: string,
    @Param('semester') semester: string,
    @Param('month') month: number,
    @Param('ruleId') ruleId: number,
    @Query('assessedName') assessedName?: string
  ) {
    return await this.service.getScoreReportForQuestion(
      enterpriseCode,
      semester,
      month,
      ruleId,
      {
        assessedName,
      }
    );
  }

  @Get('/one/:enterpriseCode/:semester/:assessedCode', {
    summary: '按被考核人编号查询成绩报告',
  })
  async one(
    @Param('enterpriseCode') enterpriseCode: string,
    @Param('semester') semester: string,
    @Param('assessedCode') assessedCode: string
  ) {
    const res = await this.service.getScoreReportForOne(
      enterpriseCode,
      semester,
      assessedCode
    );
    return res;
  }

  @Patch('/detail/:detailId')
  async editQuestionScore(
    @Param('detailId') detailId: number,
    @Body('score') score: number
  ) {
    await this.service.editQuestionScore(detailId, score);
    return true;
  }

  @Get('/export/:enterpriseCode/:semester/:month/:ruleId', {
    summary: '导出成绩报表为Excel',
  })
  async exportToExcel(
    @Param('enterpriseCode') enterpriseCode: string,
    @Param('semester') semester: string,
    @Param('month') month: number,
    @Param('ruleId') ruleId?: number
  ) {
    try {
      // 获取Excel文件Buffer
      const buffer = await this.service.exportScoreReportToExcel(
        enterpriseCode,
        semester,
        month,
        ruleId
      );

      // 设置响应头
      this.ctx.set({
        'Content-Type':
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename=score-report-${enterpriseCode}-${semester}-${month}-${ruleId}.xlsx`,
      });

      // 返回文件流
      return { buffer };
    } catch (error) {
      this.ctx.throw(500, `导出Excel失败: ${error.message}`);
    }
  }

  @Get('/exportProgress/:enterpriseCode/:semester/:month', {
    summary: '导出成绩报表为Excel',
  })
  async exportTaskProgress(
    @Param('enterpriseCode') enterpriseCode: string,
    @Param('semester') semester: string,
    @Param('month') month: number
  ) {
    try {
      // 获取Excel文件Buffer
      const buffer = await this.service.exportTaskProgress(
        enterpriseCode,
        semester,
        month
      );

      // 设置响应头
      this.ctx.set({
        'Content-Type':
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename=score-report-${enterpriseCode}-${semester}-${month}.xlsx`,
      });

      // 返回文件流
      return { buffer };
    } catch (error) {
      this.ctx.throw(500, `导出Excel失败: ${error.message}`);
    }
  }

  @Get(
    '/sync-questionnaire/:enterpriseCode/:semester/:month/:ruleId/:questionnaireId',
    {
      summary: '同步问卷得分',
    }
  )
  async syncQuestionnaireScores(
    @Param('enterpriseCode') enterpriseCode: string,
    @Param('semester') semester: string,
    @Param('month') month: number,
    @Param('ruleId') ruleId: number,
    @Param('questionnaireId') questionnaireId: number
  ) {
    const result = await this.service.syncQuestionnaireScores(
      enterpriseCode,
      semester,
      month,
      ruleId,
      questionnaireId
    );
    return { success: true, ...result };
  }
}
