/*
 * @Description: 数据初始化
 * @Date: 2025-01-06 14:12:26
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-06-20 16:00:23
 */
import { App, Autoload, Init, Inject, Scope, ScopeEnum } from '@midwayjs/core';
import * as koa from '@midwayjs/koa';
import * as bullmq from '@midwayjs/bullmq';

@Autoload()
@Scope(ScopeEnum.Singleton)
export class AutoloadListener {
  @App('koa')
  app: koa.Application;

  @Inject()
  bullmqFramework: bullmq.Framework;

  @Init()
  async init() {
    // 只在主进程初始化
    const env = this.app.getEnv();
    console.log('env: ', env);
    if (
      typeof process.env.NODE_APP_INSTANCE === 'undefined' ||
      process.env.NODE_APP_INSTANCE === '0'
    ) {
      // 启动时执行一次生成月度任务
      // 获取 Processor 相关的队列
      const generateMonthlyTasks = this.bullmqFramework.getQueue(
        'generate-monthly-tasks'
      );
      // 立即添加这个任务
      await generateMonthlyTasks?.addJobToQueue({});
    }
  }
}
