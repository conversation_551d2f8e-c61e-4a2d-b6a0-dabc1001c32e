import { BaseService } from '../common/BaseService';
import { ObservationPoint } from '../entity/observation-point.entity';
import { ModelCtor } from 'sequelize-typescript';
import { CustomError } from '../error/custom.error';
import { Provide } from '@midwayjs/core';
import { Scoring } from '../entity/scoring.entity';
import { Rule } from '../entity/rule.entity';
import { AssessmentPlan } from '../entity/assessment-plan.entity';
import { ExcelColumnImportOptions, ExcelUtil } from '../common/ExcelUtil';
import { IObservationPoint } from '../entity/interface';

/**
 * 观测点服务
 */
@Provide()
export class ObservationPointService extends BaseService<ObservationPoint> {
  constructor() {
    super('观测点');
  }

  /**
   * 获取模型
   */
  protected getModel(): ModelCtor<ObservationPoint> {
    return ObservationPoint;
  }

  /**
   * 创建观测点
   * @param observationPoint 观测点信息
   */
  async createObservationPoint(
    observationPoint: Partial<ObservationPoint>
  ): Promise<ObservationPoint> {
    // 验证观测点有效性
    await this.validateObservationPoint(observationPoint);
    return this.create(observationPoint);
  }

  /**
   * 批量维护观测点
   * @param scoringId 赋分组ID
   * @param observationPoints 观测点列表
   */
  async batchMaintainObservationPoints(
    scoringId: number,
    observationPoints: ObservationPoint[]
  ): Promise<ObservationPoint[]> {
    // 验证赋分组是否存在
    const scoring = await Scoring.findByPk(scoringId, {
      include: [{ model: Rule, include: [AssessmentPlan] }],
    });

    if (!scoring) {
      throw new CustomError('赋分组不存在', 404);
    }

    // 验证方案状态
    if (scoring.rule?.assessmentPlan?.status === 'published') {
      throw new CustomError('已发布的方案不允许修改', 403);
    }

    // 开始事务
    const transaction = await ObservationPoint.sequelize.transaction();

    try {
      const result: ObservationPoint[] = [];

      // 先删除该赋分组下的所有观测点
      await ObservationPoint.destroy({
        where: { scoringId },
        transaction,
      });

      // 处理每个观测点
      for (const point of observationPoints) {
        // 设置赋分组ID
        point.scoringId = scoringId;
        // 创建新观测点
        await this.validateObservationPoint(point);
        const newPoint = await ObservationPoint.create(point, {
          transaction,
        });
        result.push(newPoint);
      }

      // 提交事务
      await transaction.commit();
      return result;
    } catch (error) {
      console.error(error);
      // 回滚事务
      await transaction.rollback();
      this.logger.error(
        {
          message: '批量维护观测点失败',
          source: '业务',
          operation: '批量维护观测点',
          data: {
            scoringId,
            observationPoints,
          },
        },
        error
      );
      throw error;
    }
  }

  /**
   * 验证观测点有效性
   * @param observationPoint 观测点信息
   */
  /**
   * 根据赋分组ID获取观测点列表
   * @param scoringId 赋分组ID
   * @returns 观测点列表
   */
  async findByScoringId(scoringId: number): Promise<ObservationPoint[]> {
    try {
      // 验证赋分组是否存在
      const scoring = await Scoring.findByPk(scoringId);
      if (!scoring) {
        throw new CustomError('赋分组不存在', 404);
      }

      // 查询该赋分组下的所有观测点
      const { list } = await this.findAll({
        query: { scoringId },
        order: [['orderIndex', 'ASC']],
      });

      return list;
    } catch (error) {
      this.logger.error(
        {
          message: `根据赋分组ID获取观测点失败: ${error.message}`,
          source: '数据库',
          operation: 'findByScoringId',
          data: { scoringId },
        },
        error
      );
      throw new CustomError(`根据赋分组ID获取观测点失败: ${error.message}`);
    }
  }

  async import(scoringId: number, filePath: string) {
    const options: {
      startLineNo?: number;
      columns: ExcelColumnImportOptions[];
    } = {
      startLineNo: 3,
      columns: [
        {
          title: '观测点名称',
          enName: 'pointName',
          maxLength: 100,
        },
        {
          title: '基础分值',
          enName: 'baseScore',
          type: 'number',
          min: 0,
          max: 100,
          allowEmpty: false,
        },
        {
          title: '打分说明',
          enName: 'description',
          allowEmpty: true,
          maxLength: 200,
        },
      ],
    };
    const { data, errInfo } = ExcelUtil.readToJson(filePath, options);
    if (errInfo && Object.keys(errInfo).length > 0) {
      return {
        success: false,
        errInfo,
      };
    }
    const points: Omit<IObservationPoint, 'pointId'>[] = [];
    for (let i = 0; i < data.length; i++) {
      const point = data[i];
      points.push({
        scoringId,
        pointName: point.pointName,
        baseScore: point.baseScore,
        description: point.description,
        orderIndex: i + 1,
      });
    }
    console.log(points);
    await this.batchMaintainObservationPoints(
      scoringId,
      points as ObservationPoint[]
    );
    return {
      success: true,
      errInfo: null,
    };
  }

  /**
   * 创建或更新前的验证
   * @param observationPoint 观测点信息
   */
  private async validateObservationPoint(
    observationPoint: Partial<ObservationPoint>
  ): Promise<void> {
    // 验证赋分组是否存在
    if (observationPoint.scoringId) {
      const scoring = await Scoring.findByPk(observationPoint.scoringId, {
        include: [{ model: Rule, include: [AssessmentPlan] }],
      });

      if (!scoring) {
        throw new CustomError('赋分组不存在', 404);
      }

      // 验证方案状态
      if (scoring.rule?.assessmentPlan?.status === 'published') {
        throw new CustomError('已发布的方案不允许修改', 403);
      }
    }

    // 验证基础分值
    if (
      observationPoint.baseScore !== undefined &&
      observationPoint.baseScore < 0
    ) {
      throw new CustomError('基础分值必须大于等于0', 400);
    }
  }
}
