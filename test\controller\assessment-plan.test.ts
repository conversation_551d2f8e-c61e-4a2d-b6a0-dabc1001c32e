import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework, Application } from '@midwayjs/koa';

const testData = {
  planName: '2024-2025年第二学期考核',
  semester: '2024202502',
  startMonth: '2025-01',
  endMonth: '2025-06',
};

describe('test /assessment-plan', () => {
  let app: Application;
  let planId: string;

  beforeAll(async () => {
    try {
      app = await createApp<Framework>();
    } catch (err) {
      console.error('test beforeAll error', err);
      throw err;
    }
  });

  afterAll(async () => {
    await close(app);
  });

  it('should create plan successfully', async () => {
    // 准备测试数据
    const planData = {...testData};

    // 发送请求
    const result = await createHttpRequest(app)
      .post('/assessment-plan/')
      .send(planData);

    console.log('result.status', result.status);
    console.log('result.body', result.body);
    console.log('result.body.msg', result.body.msg);
    console.log('result.body.data', result.body.data);
    // 验证响应
    expect(result.status).toBe(201);
    expect(result.body.msg).toBe('OK');
    expect(result.body.data).toHaveProperty('planId');
    planId = result.body.data.planId;
  });

  it('should return 400 when time conflict', async () => {
    // 准备测试数据
    const planData = {...testData};

    // 发送请求
    const result = await createHttpRequest(app)
      .post('/assessment-plan/')
      .send(planData);

    // 验证响应
    expect(result.status).toBe(200);
    expect(result.body.errCode).toBe(400);
    expect(result.body.msg).toBe('时间段冲突');
  });

  it('should return 400 when missing required fields', async () => {
    // 准备测试数据（缺少semester字段）
    const { semester, ...info } = testData;

    // 发送请求
    const result = await createHttpRequest(app)
      .post('/assessment-plan/')
      .send(info);

    console.log('result.status', result.status);
    console.log('result.body', result.body);
    // 验证响应
    expect(result.status).toBe(200);
    expect(result.body.errCode).toBe(422);
    expect(result.body.msg).toBe('学期不能为空');
  });

  it('should delete plan successfully', async () => {
    // 发送请求
    const result = await createHttpRequest(app).delete(
      `/assessment-plan/${planId}`
    );
    // 验证响应
    expect(result.status).toBe(200);
    expect(result.body.msg).toBe('OK');
  });
});
