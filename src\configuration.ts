import { Configuration, App } from '@midwayjs/core';
import * as koa from '@midwayjs/koa';
import * as validate from '@midwayjs/validate';
import * as info from '@midwayjs/info';
import * as axios from '@midwayjs/axios';
import { join } from 'path';
import * as sequelize from '@midwayjs/sequelize';
import { ParamErrorFilter } from './filter/param.filter';
import { DatabaseFilter } from './filter/database.filter';
import { AxiosFilter } from './filter/axios.filter';
import { NotFoundFilter } from './filter/notfound.filter';
import { NoAuthFilter } from './filter/noauth.filter';
import { CustomErrorFilter } from './filter/custom.filter';
import { DefaultErrorFilter } from './filter/default.filter';
// import { JWTMiddleware } from './middleware/jwt.middleware';
import { RequestLoggerMiddleware } from './middleware/requestlogger.middleware';
import { FormatMiddleware } from './middleware/format.middleware';
import * as jwt from '@midwayjs/jwt';
import * as busboy from '@midwayjs/busboy';
import * as bullmq from '@midwayjs/bullmq';

@Configuration({
  imports: [
    koa,
    validate,
    sequelize,
    busboy,
    jwt,
    axios,
    bullmq,
    {
      component: info,
      enabledEnvironment: ['local'],
    },
  ],
  importConfigs: [join(__dirname, './config')],
})
export class MainConfiguration {
  @App('koa')
  app: koa.Application;

  async onReady() {
    const env = this.app.getEnv();
    // add middleware
    this.app.useMiddleware(
      ['local', 'unittest', 'test'].includes(env)
        ? [RequestLoggerMiddleware, FormatMiddleware]
        : [FormatMiddleware]
    );
    // add filter
    this.app.useFilter([
      NotFoundFilter,
      NoAuthFilter,
      ParamErrorFilter,
      DatabaseFilter,
      AxiosFilter,
      CustomErrorFilter,
      DefaultErrorFilter,
    ]);
  }
}
