# 教师月度考评系统 - 开发说明文档

## 项目概述

教师月度考评系统是一个基于 Midway.js 框架开发的 Node.js 应用，用于管理教师的月度考核评价流程。系统支持考核方案配置、自动任务生成、多维度评分、第三方问卷集成和结果公示等功能。

## 技术栈

- **框架**: Midway.js 3.x
- **数据库**: MySQL + Sequelize ORM
- **消息队列**: BullMQ + Redis
- **文件处理**: node-xlsx, xlsx
- **HTTP客户端**: Axios
- **验证**: @midwayjs/validate

## 核心业务模块

### 1. 考核方案管理 (`assessment-plan`)
- **实体**: `AssessmentPlan`
- **控制器**: `AssessmentPlanController`
- **功能**: 创建、配置、发布考核方案
- **关键字段**: 方案名称、学期、评分类型(分值/星级)、权重配置

### 2. 考核任务管理 (`assessment-task`)
- **实体**: `AssessmentTask`
- **服务**: `AssessmentTaskService`
- **功能**: 根据方案自动生成月度考核任务
- **状态**: `pending` | `completed`

### 3. 评分系统 (`score`)
- **实体**: `Score`
- **控制器**: `ScoreController`
- **功能**: 考核人员按观测点对被考核人评分
- **支持**: 分值评分、星级评分、备注、附件

### 4. 成绩报表 (`score-report`)
- **实体**: `ScoreReport`, `ScoreReportDetail`
- **服务**: `ScoreReportService`
- **功能**: 生成考核报表、Excel导出、进度统计

### 5. 第三方问卷集成 (`questionnaire`)
- **服务**: `QuestionnaireService`
- **功能**: 获取问卷数据、教师成绩统计
- **接口**: 与问卷系统(端口3141)集成

## 数据模型层次结构

```
考核方案 (AssessmentPlan)
├── 考核规则 (Rule)
│   ├── 赋分规则 (Scoring)
│   │   └── 观测点 (ObservationPoint)
│   └── 考核任务 (AssessmentTask)
│       └── 评分记录 (Score)
└── 成绩报表 (ScoreReport)
    └── 报表详情 (ScoreReportDetail)
```

## 关键业务流程

### 1. 考核方案发布流程
1. 创建考核方案 (草稿状态)
2. 配置考核规则和赋分规则
3. 检查配置完整性
4. 发布方案 → 自动生成当月任务

### 2. 月度任务生成
- **触发**: 方案发布时自动生成当月任务
- **逻辑**: `AssessmentTaskService.generateMonthlyTasks()`
- **支持**: 增量更新，避免重复创建

### 3. 评分流程
1. 考核人员查看待办任务
2. 按观测点逐项评分
3. 系统验证评分有效性
4. 任务完成后自动更新状态

## 开发环境配置

### 环境要求
- Node.js >= 12.0.0
- MySQL 数据库
- Redis (用于消息队列)

### 启动步骤
```bash
# 安装依赖
npm i

# 开发模式启动
npm run dev

# 访问地址
http://localhost:7001/
```

### 配置文件
- **数据库**: `src/config/config.default.ts`
- **Redis**: BullMQ配置 (************:6379)
- **第三方接口**: 
  - 接口转发中心: http://***********:1002
  - 问卷系统: http://***********:3141

## API 接口说明

### 考核方案相关
- `GET /assessment-plans` - 查询考核方案列表
- `POST /assessment-plans` - 创建考核方案
- `GET /assessment-plans/:planId` - 获取方案详情
- `PUT /assessment-plans/:planId` - 更新考核方案
- `DELETE /assessment-plans/:planId` - 删除考核方案
- `PATCH /assessment-plans/:planId/status` - 发布/撤销方案
- `GET /assessment-plans/:planId/check` - 检查方案配置完整性
- `POST /assessment-plans/:planId/copy` - 复制考核方案

### 考核任务相关
- `GET /assessment-task/groups/:enterpriseCode/:semester/:month` - 获取月度考核分组
- `PUT /dev-tools/generate-monthly-tasks/:planId` - 手动生成月度任务

### 评分相关
- `PATCH /score/:taskId/:scoreId` - 提交评分

### 报表相关
- `GET /score-report/list/:enterpriseCode/:semester/:month/:ruleId` - 查询成绩报告
- `GET /score-report/export/*` - 导出Excel报表

### 问卷集成
- `GET /questionnaire/:schoolCode` - 获取学校问卷列表
- `GET /questionnaire/:questionnaireId/teacher-scores` - 获取教师问卷成绩

### 复制方案接口详细说明

#### POST /assessment-plans/:planId/copy - 复制考核方案

**功能**: 基于现有方案创建新的方案，包括深度复制所有关联的规则、赋分规则和观测点

**请求参数**:
- `planId` (路径参数): 源方案ID
- 请求体 (JSON):
  ```json
  {
    "planName": "新方案名称（可选，不填则自动生成）",
    "semester": "目标学期（可选）",
    "semesterName": "目标学期名称（可选）",
    "startMonth": "开始月份（可选）",
    "endMonth": "结束月份（可选）",
    "description": "方案描述（可选）"
  }
  ```

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "planId": 123,
    "planName": "2024年下学期教师考核方案",
    "message": "方案复制成功"
  }
}
```

**特性**:
- 自动处理唯一约束冲突（方案名称自动添加"(复制)"后缀）
- 深度复制所有关联数据（规则、赋分规则、观测点）
- 确保数据隔离性，复制的方案状态强制为草稿
- 支持事务回滚，确保数据一致性

## 开发注意事项

1. **事务处理**: 任务生成等关键操作使用数据库事务
2. **日志记录**: 使用 `CustomLogger` 记录业务操作日志
3. **错误处理**: 统一使用 `CustomError` 处理业务异常
4. **数据验证**: 使用 `@midwayjs/validate` 进行参数验证
5. **权限控制**: 开发工具接口使用 `DevTestMiddleware` 中间件

## 部署说明

```bash
# 构建项目
npm run build

# 生产环境启动
npm start

# 使用部署脚本
./scripts/deploy.sh 60  # 生产环境
./scripts/deploy.sh 49  # 测试环境
```

## 测试

```bash
# 运行单元测试
npm test

# 代码覆盖率
npm run cov

# 代码风格检查
npm run lint
```

## 项目结构

```
src/
├── controller/          # 控制器层
├── service/            # 服务层
├── entity/             # 数据实体
├── dto/                # 数据传输对象
├── middleware/         # 中间件
├── processor/          # 任务处理器
├── common/             # 公共工具类
└── config/             # 配置文件
```

## 更多信息

如需进一步了解 Midway.js 框架，参见 [midway 文档](https://midwayjs.org)。