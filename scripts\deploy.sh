#!/bin/bash

# 检查环境参数
if [ -z "$1" ]; then
  echo "错误: 请指定环境参数"
  echo "用法: ./deploy.sh <环境>"
  echo "可用环境: 60 (生产环境60), 49 (测试环境)"
  exit 1
fi

# 设置环境变量
case "$1" in
60 | 40)
  NODE_ENV="$1"
  echo "使用环境: $NODE_ENV"
  ;;
*)
  echo "错误: 无效的环境参数 '$1'"
  echo "可用环境: 60 (生产环境60), 49 (测试环境)"
  exit 1
  ;;
esac

echo "开始编译项目……"
npm install            # 安装开发期依赖
npm run build          # 构建项目
npm prune --production # 移除开发依赖

pm2_name="teacherEvaluation"
# 检查服务是否存在
if pm2 list | grep -q "${pm2_name}"; then
  echo "服务已存在，重启"
  pm2 restart ${pm2_name}
else
  echo "服务不存在，创建并启动"
  NODE_ENV=${NODE_ENV} pm2 start ./bootstrap.js --name ${pm2_name} -i 2 # 启动服务
fi
