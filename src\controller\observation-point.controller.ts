import {
  Inject,
  Controller,
  Put,
  Get,
  Del,
  Body,
  Param,
  HttpCode,
  Post,
} from '@midwayjs/core';
import { ObservationPointService } from '../service/observation-point.service';
import { CreateObservationPointDTO } from '../dto/observation-point.dto';
import { CustomError } from '../error/custom.error';
import { ObservationPoint } from '../entity/observation-point.entity';

/**
 * 观测点控制器
 */
@Controller('/scoring/:scoringId/observation-points')
export class ObservationPointController {
  @Inject()
  observationPointService: ObservationPointService;

  @Put('/', { summary: '批量维护观测点' })
  @HttpCode(200)
  async batchMaintain(
    @Param('scoringId') scoringId: number,
    @Body() body: CreateObservationPointDTO
  ) {
    const res =
      await this.observationPointService.batchMaintainObservationPoints(
        scoringId,
        body.list.map((point, index) => ({
          ...point,
          scoringId,
          orderIndex: index,
        })) as ObservationPoint[]
      );
    return res;
  }

  @Get('/', { summary: '获取赋分规则下的观测点列表' })
  async list(@Param('scoringId') scoringId: number) {
    try {
      const observationPoints =
        await this.observationPointService.findByScoringId(scoringId);
      return { list: observationPoints };
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(`获取观测点列表失败: ${error.message}`, 500);
    }
  }

  @Get('/:pointId', { summary: '获取观测点详情' })
  async getDetail(
    @Param('scoringId') scoringId: number,
    @Param('pointId') pointId: number
  ) {
    try {
      const observationPoint = await this.observationPointService.findById(
        pointId
      );
      if (!observationPoint) {
        throw new CustomError('观测点不存在', 404);
      }
      if (observationPoint.scoringId !== scoringId) {
        throw new CustomError('观测点不属于指定赋分规则', 400);
      }
      return observationPoint;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(`获取观测点详情失败: ${error.message}`, 500);
    }
  }

  @Del('/:pointId', { summary: '删除观测点' })
  async delete(
    @Param('scoringId') scoringId: number,
    @Param('pointId') pointId: number
  ) {
    try {
      // 验证观测点存在且属于指定赋分规则
      const observationPoint = await this.observationPointService.findById(
        pointId
      );
      if (!observationPoint) {
        throw new CustomError('观测点不存在', 404);
      }
      if (observationPoint.scoringId !== scoringId) {
        throw new CustomError('观测点不属于指定赋分规则', 400);
      }

      await this.observationPointService.delete({ pointId });
      return true;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(`删除观测点失败: ${error.message}`, 500);
    }
  }

  @Post('/import', { summary: '导入观测点' })
  async import(
    @Param('scoringId') scoringId: number,
    @Body('filePath') filePath: string
  ) {
    return this.observationPointService.import(scoringId, filePath);
  }
}
