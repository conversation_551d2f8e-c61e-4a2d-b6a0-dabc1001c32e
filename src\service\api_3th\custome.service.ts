/*
 * @Description: 对接客户系统相关接口
 * @Date: 2025-03-29 10:17:13
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-03-29 18:46:40
 */
import { Inject, Provide } from '@midwayjs/core';
import { APIManager } from '../../common/APIManager';
import { IUserInfo } from '../../interface';

@Provide()
export class Custome {
  @Inject()
  apiManager: APIManager;

  /**
   * 查询学期列表
   * @param enterpriseCode 学校编号
   * @returns 学期列表
   */
  async getSemesterList(enterpriseCode: string) {
    const result = await this.apiManager.send({
      apiCode: 'getSemesterList',
      params: enterpriseCode,
    });
    return result;
  }

  /**
   * 查询角色列表
   * @param enterpriseCode 学校编号
   * @returns 角色列表
   * */
  async getSchoolRoles(enterpriseCode: string) {
    const result = await this.apiManager.send({
      apiCode: 'getSchoolRoles',
      query: { enterpriseCode },
    });
    return { list: result.list };
  }

  /**
   * 查询角色下的用户列表
   * @param enterpriseCode 学校编号
   * @param roleCode 角色编号
   * @param limit 每页数量
   * @param offset 页码
   * @returns 用户列表
   * */
  async getSchoolUserRoleList(
    enterpriseCode: string,
    roleCode: string,
    limit?: number,
    offset?: number
  ): Promise<{ count: number; list: IUserInfo[] }> {
    const result = await this.apiManager.send({
      apiCode: 'getSchoolUserRoleList',
      query: { enterpriseCode, roleCode, limit, offset },
    });
    return result;
  }

  /**
   * 获取指定学校下的所有成员
   *
   * @param {string} enterpriseCode 学校编号
   * @return {*} list 成员列表
   * @memberof Custome
   */
  async getMembers(
    enterpriseCode: string
  ): Promise<{ count: number; list: IUserInfo[] }> {
    const result = await this.apiManager.send({
      apiCode: 'getMembers',
      query: { enterpriseCode },
    });
    return result;
  }
}
