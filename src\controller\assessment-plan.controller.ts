import {
  Inject,
  Controller,
  Post,
  Del,
  Get,
  Put,
  Patch,
  Body,
  HttpCode,
  Param,
  Query,
} from '@midwayjs/core';
import { AssessmentPlanService } from '../service/assessment-plan.service';
import { RuleService } from '../service/rule.service';
import { AssessmentTaskService } from '../service/assessment-task.service';
import { PublicationRecordService } from '../service/publication-record.service';
import { CustomError } from '../error/custom.error';
import { CustomLogger } from '../common/CustomLogger';
import {
  CreateAssessmentPlanDTO,
  UpdateAssessmentPlanDTO,
  CopyAssessmentPlanDTO,
} from '../dto/assessment-plan.dto';
import { Op } from 'sequelize';

/**
 * 考核方案控制器
 */
@Controller('/assessment-plans')
export class AssessmentPlanController {
  @Inject()
  assessmentPlanService: AssessmentPlanService;

  @Inject()
  ruleService: RuleService;

  @Inject()
  assessmentTaskService: AssessmentTaskService;

  @Inject()
  publicationRecordService: PublicationRecordService;

  @Inject()
  logger: CustomLogger;

  @Get('/', { summary: '查询考核方案列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      enterpriseCode,
      semester,
      ...queryInfo
    } = query;
    void o;
    void l;
    if (!enterpriseCode) {
      throw new CustomError('学校编码不能为空', 400);
    }
    if (!semester) {
      throw new CustomError('学期不能为空', 400);
    }

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // planName支持模糊查询
    if (queryInfo.planName) {
      queryInfo.planName = {
        [Op.like]: `%${queryInfo.planName}%`,
      };
    }
    return this.assessmentPlanService.findAll({
      query: { ...queryInfo, enterpriseCode, semester },
      offset,
      limit,
      order: [['startMonth', 'ASC']],
    });
  }

  /**
   * 创建考核方案
   * @param plan 方案信息
   */
  @Post('/')
  @HttpCode(201)
  async createPlan(@Body() plan: CreateAssessmentPlanDTO) {
    // 创建方案
    const newPlan = await this.assessmentPlanService.createPlan({
      ...plan,
      status: 'draft', // 默认为草稿状态
    });

    return {
      planId: newPlan.planId,
    };
  }

  /**
   * 查询考核方案
   * @param planId 方案ID
   */
  @Get('/:planId')
  async getPlan(@Param('planId') planId: string) {
    const plan = await this.assessmentPlanService.findById(planId);
    if (!plan) {
      throw new CustomError('指定的考核方案不存在', 404);
    }

    return plan.toJSON();
  }

  /**
   * 更新考核方案
   * @param plan 方案信息
   */
  @Put('/:planId')
  async updatePlan(
    @Param('planId') planId: string,
    @Body() plan: UpdateAssessmentPlanDTO
  ) {
    const existingPlan = await this.assessmentPlanService.findById(planId);
    if (!existingPlan) {
      throw new CustomError('指定的考核方案不存在', 404);
    }
    if (existingPlan.status !== 'draft') {
      // 已发布的方案只允许修改填写日期
      if (plan.fillableDates) {
        await existingPlan.update({ fillableDates: plan.fillableDates });
        return true;
      }
      throw new CustomError('已发布的方案不允许修改', 400);
    }
    // 更新方案
    await this.assessmentPlanService.update({ planId }, plan);
    return true;
  }

  /**
   * 删除考核方案
   * @param planId 方案ID
   */
  @Del('/:planId')
  async deletePlan(@Param('planId') planId: string) {
    const existingPlan = await this.assessmentPlanService.findById(planId);
    if (!existingPlan) {
      throw new CustomError('指定的考核方案不存在', 404);
    }
    if (existingPlan.status !== 'draft') {
      throw new CustomError('已发布的方案不允许修改', 400);
    }
    // 删除方案
    await this.assessmentPlanService.delete({ planId });
    return true;
  }

  /**
   * 更新考核方案状态
   * @param planId 方案ID
   * @param status 状态信息
   */
  @Patch('/:planId/status')
  async updatePlanStatus(
    @Param('planId') planId: number,
    @Body() body: { status: 'draft' | 'published' }
  ) {
    const { status } = body;
    if (!status) {
      throw new CustomError('状态不能为空', 400);
    }

    // 验证状态有效性
    const validStatus = ['draft', 'published'];
    if (!validStatus.includes(status)) {
      throw new CustomError(
        `无效的状态值，必须为 ${validStatus.join(', ')} 之一`,
        400
      );
    }
    const plan = await this.assessmentPlanService.findById(planId);
    if (!plan) {
      throw new CustomError('指定的考核方案不存在', 404);
    }

    if (plan.status === status) {
      throw new CustomError('当前状态与目标状态一致，无需更新', 400);
    }

    const currentMonth = new Date().getMonth() + 1;
    if (status === 'published') {
      // 状态由草稿变为发布时，立即生成当月任务
      // 检查信息是否完整
      if (!(await this.assessmentPlanService.checkConfiguration(planId))) {
        throw new CustomError('考核方案配置不完整，无法发布', 400);
      }
      // 检查与其他已发布的方案是否有冲突
      const conflictPlans = await this.assessmentPlanService.findConflictPlans(
        planId
      );
      if (conflictPlans.length > 0) {
        throw new CustomError(
          '当前考核方案与已发布的方案有冲突，请检查后再发布'
        );
      }
      // 生成当月任务
      await this.assessmentTaskService.generateMonthlyTasks(
        planId,
        currentMonth
      );
    } else {
      // 状态由发布变为草稿时，删除当月任务，界面要有提示
      // 删除当月所有任务
      await this.assessmentTaskService.deleteTasksByPlanIdAndMonth(planId, [
        currentMonth,
      ]);
      this.logger.info({
        message: '已删除当月所有任务',
        source: '业务',
        operation: 'updatePlanStatus',
        data: { planId, planName: plan.planName, month: currentMonth },
      });
      // 删除当月已发布记录和报表数据
      await this.publicationRecordService.unpubRecord(
        plan.enterpriseCode,
        plan.semester,
        currentMonth
      );
    }

    // 更新状态
    await this.assessmentPlanService.update({ planId }, { status });
    this.logger.info({
      message: `【考核方案${status === 'published' ? '发布' : '撤消'}】成功`,
      source: '业务',
      operation: 'updatePlanStatus',
      data: { planId, planName: plan.planName },
    });
    return true;
  }

  @Get('/:planId/check', { summary: '检查方案配置完整性' })
  async checkConfiguration(@Param('planId') planId: number) {
    return await this.assessmentPlanService.checkConfiguration(planId);
  }

  /**
   * 复制考核方案
   * @param planId 源方案ID
   * @param copyData 复制时的新数据
   */
  @Post('/:planId/copy', { summary: '复制考核方案' })
  @HttpCode(201)
  async copyPlan(
    @Param('planId') planId: number,
    @Body() copyData: CopyAssessmentPlanDTO
  ) {
    try {
      // 验证源方案是否存在
      const sourcePlan = await this.assessmentPlanService.findById(planId);
      if (!sourcePlan) {
        throw new CustomError('源方案不存在', 404);
      }

      // 执行复制操作
      const newPlan = await this.assessmentPlanService.copyPlan(
        planId,
        copyData
      );

      this.logger.info({
        message: '方案复制成功',
        source: '业务',
        operation: 'copyPlan',
        data: {
          sourcePlanId: planId,
          sourcePlanName: sourcePlan.planName,
          newPlanId: newPlan.planId,
          newPlanName: newPlan.planName,
        },
      });

      return {
        planId: newPlan.planId,
        planName: newPlan.planName,
        message: '方案复制成功',
      };
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      this.logger.error(
        {
          message: `复制方案失败: ${error.message}`,
          source: '业务',
          operation: 'copyPlan',
          data: { planId, copyData },
        },
        error
      );
      throw new CustomError(`复制方案失败: ${error.message}`, 500);
    }
  }
}
