import { Controller, Get, Inject, Param } from '@midwayjs/core';
import { QuestionnaireService } from '../service/api_3th/questionnaire.service';

@Controller('/questionnaire')
export class QuestionnaireController {
  @Inject()
  service: QuestionnaireService;

  @Get('/:schoolCode', {
    summary: '获取指定学校近1年内的问卷列表',
  })
  async getQuestionnairesBySchool(@Param('schoolCode') schoolCode: string) {
    return await this.service.getQuestionnairesBySchool(schoolCode);
  }

  @Get('/:questionnaireId/teacher-scores', {
    summary: '获取指定问卷的教师成绩统计',
  })
  async getTeacherScoresByQuestionnaire(
    @Param('questionnaireId') questionnaireId: number
  ) {
    return await this.service.getTeacherScoresByQuestionnaire(questionnaireId);
  }
}
