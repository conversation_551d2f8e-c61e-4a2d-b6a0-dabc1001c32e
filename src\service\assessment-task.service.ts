import { Inject, Provide } from '@midwayjs/core';
import { Op, WhereOptions } from 'sequelize';
import { ModelCtor } from 'sequelize-typescript';
import * as _ from 'lodash';
import { BaseService } from '../common/BaseService';
import { AssessmentTask } from '../entity/assessment-task.entity';
import { CustomError } from '../error/custom.error';
import { AssessmentPlan } from '../entity/assessment-plan.entity';
import { Score } from '../entity/score.entity';
import { CustomLogger, LoggerMsgProps } from '../common/CustomLogger';
import { IAssessmentTask } from '../entity/interface';
import { Custome } from './api_3th/custome.service';
import { Scoring } from '../entity/scoring.entity';
import { ObservationPoint } from '../entity/observation-point.entity';
import { Rule } from '../entity/rule.entity';
import { PublicationRecord } from '../entity/publication-record.entity';

/**
 * 考核任务服务
 */
@Provide()
export class AssessmentTaskService extends BaseService<AssessmentTask> {
  @Inject()
  userService: Custome;

  @Inject()
  logger: CustomLogger;

  constructor() {
    super('考核任务');
  }

  /**
   * 获取模型
   */
  protected getModel(): ModelCtor<AssessmentTask> {
    return AssessmentTask;
  }

  /**
   * 计算并更新任务状态
   * @param enterpriseCode 企业编码
   * @param semester 学期
   * @param month 月份
   */
  private async caclTaskStatus(
    enterpriseCode: string,
    semester: string,
    month: number
  ) {
    if (!enterpriseCode || !semester || !month) return;
    const plans = await AssessmentPlan.findAll({
      where: {
        enterpriseCode,
        semester,
        status: 'published',
      },
      attributes: ['planId'],
    });
    const planIds = plans.map(plan => plan.planId);
    const queryOpt: WhereOptions = {
      planId: planIds,
      month,
    };
    const list = await AssessmentTask.findAll({
      where: queryOpt,
      attributes: ['taskId', 'status'],
    });
    await Promise.all(
      list.map(async item => {
        const scores = await Score.findOne({
          where: {
            taskId: item.taskId,
            scoreValue: {
              [Op.ne]: null,
            },
          },
          attributes: ['scoreId'],
        });
        if (scores) {
          item.status = 'completed';
        } else {
          item.status = 'pending';
        }
        await item.save();
      })
    );
  }

  /**
   * 获取月度考核分组
   * @param enterpriseCode 企业编码
   * @param semester 学期
   * @param month 月份
   */
  async getGroups(enterpriseCode: string, semester: string, month: number) {
    this.caclTaskStatus(enterpriseCode, semester, month);
    return AssessmentTask.findAll({
      where: {
        month,
      },
      include: [
        {
          model: AssessmentPlan,
          where: {
            enterpriseCode,
            semester,
            status: 'published',
          },
          attributes: [],
        },
      ],
      attributes: ['ruleId', 'ruleName'],
      group: ['ruleId', 'ruleName'],
      order: [['ruleId', 'ASC']],
    });
  }

  /**
   * 按考核规则获取分析数据
   * @param enterpriseCode 学校编号
   * @param semester 学期编号
   * @param month 月份
   * @param ruleId 规则ID
   * @returns res
   */
  async getAnalysisDataByGroup(
    enterpriseCode: string,
    semester: string,
    month: number,
    ruleId: number
  ) {
    const list = await AssessmentTask.findAll({
      where: {
        month,
        ruleId,
      },
      include: [
        {
          model: AssessmentPlan,
          where: {
            enterpriseCode,
            semester,
            status: 'published',
          },
          attributes: [],
        },
      ],
      attributes: ['status'],
    });
    return {
      total: list.length,
      pending: list.filter(item => item.status === 'pending').length,
      completed: list.filter(item => item.status === 'completed').length,
    };
  }

  /**
   * 根据考核人Code获取任务列表
   * @param assessorCode 考核人ID
   */
  async findByAssessor(assessorCode: string): Promise<AssessmentTask[]> {
    try {
      const { list } = await this.findAll({
        query: { assessorCode },
        include: [
          {
            model: AssessmentPlan,
            as: 'assessmentPlan',
          },
        ],
      });
      return list;
    } catch (error) {
      this.logger.error(
        {
          message: `根据考核人ID获取任务失败: ${error.message}`,
          source: '数据库',
          operation: 'findByAssessor',
          data: { assessorCode },
        },
        error
      );
      throw new CustomError(`根据考核人ID获取任务失败: ${error.message}`);
    }
  }

  /**
   * 根据被考核人Code获取任务列表
   * @param assessedCode 被考核人ID
   */
  async findByAssessed(assessedCode: string): Promise<AssessmentTask[]> {
    try {
      const { list } = await this.findAll({
        query: { assessedCode },
        include: [
          {
            model: AssessmentPlan,
            as: 'assessmentPlan',
          },
        ],
      });
      return list;
    } catch (error) {
      this.logger.error(
        {
          message: `根据被考核人ID获取任务失败: ${error.message}`,
          source: '数据库',
          operation: 'findByAssessed',
          data: { assessedCode },
        },
        error
      );
      throw new CustomError(`根据被考核人ID获取任务失败: ${error.message}`);
    }
  }

  /**
   * 获取任务详情，包含评分信息
   * @param taskId 任务ID
   */
  async findTaskDetail(taskId: number): Promise<AssessmentTask> {
    try {
      const task = await AssessmentTask.findByPk(taskId, {
        include: [
          {
            model: AssessmentPlan,
            as: 'assessmentPlan',
          },
          {
            model: Score,
            as: 'scores',
          },
        ],
      });

      if (!task) {
        throw new CustomError('指定的考核任务不存在');
      }

      return task;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      this.logger.error(
        {
          message: `获取任务详情失败: ${error.message}`,
          source: '数据库',
          operation: 'findTaskDetail',
          data: { taskId },
        },
        error
      );
      throw new CustomError(`获取任务详情失败: ${error.message}`);
    }
  }

  async getMonthListBySemester(
    enterpriseCode: string,
    semester: string,
    query?: Record<string, any>
  ) {
    const { assessorCode } = query || {};
    const plans = await AssessmentPlan.findAll({
      where: {
        enterpriseCode,
        semester,
        status: 'published',
      },
      attributes: [
        'planId',
        'enterpriseCode',
        'semester',
        'startMonth',
        'endMonth',
        'fillableDates',
      ],
    });
    const array = await Promise.all(
      plans.map(async plan => {
        const {
          planId,
          enterpriseCode,
          semester,
          startMonth,
          endMonth,
          fillableDates,
        } = plan;
        const where: WhereOptions = {
          planId,
        };
        if (assessorCode) {
          where.assessorCode = assessorCode;
        }
        const list = await AssessmentTask.findAll({
          where,
          attributes: [
            'month',
            [
              AssessmentTask.sequelize.fn(
                'EXTRACT',
                AssessmentTask.sequelize.literal('YEAR  FROM MIN(createdAt)')
              ),
              'year',
            ],
            [
              AssessmentTask.sequelize.literal(
                "SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END)"
              ),
              'completedCount',
            ],
            [
              AssessmentTask.sequelize.literal(
                "SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END)"
              ),
              'notCompletedCount',
            ],
            [AssessmentTask.sequelize.fn('COUNT', '*'), 'total'],
          ],
          group: ['month'],
          order: [['month', 'ASC']],
        });
        return list.map(item => {
          const data = item.toJSON() as any;
          return {
            ...data,
            planId,
            enterpriseCode,
            semester,
            startMonth,
            endMonth,
            // 填写日期为任务创建月加上填写日期后的具体日期，注意要过滤掉不合法的日期
            fillableDates: fillableDates
              .map(d => {
                const year = data.year;
                const month = String(data.month).padStart(2, '0');
                const day = String(d).padStart(2, '0');
                const str = `${year}-${month}-${day}`;
                const date = new Date(str);
                // 判断日期是否合法
                const isValidDate =
                  !isNaN(date.getTime()) &&
                  date.getMonth() + 1 === Number(month);
                return isValidDate ? date : null;
              })
              .filter(i => !!i),
          };
        });
      })
    );
    const result = _.flattenDeep(array);
    return result;
  }

  /**
   * 获取月度考核任务
   *
   * @param {string} enterpriseCode 企业编号
   * @param {number} semester 学期编号
   * @param {number} month 月份
   * @param {object} query 其他查询条件
   * @param {number} [offset] 分页偏移量
   * @param {number} [limit] 分页限制
   * @return {*} list
   * @memberof AssessmentTaskService
   */
  async getMonthlyTasks(
    enterpriseCode: string,
    semester: string,
    month: number,
    query: Partial<IAssessmentTask>,
    offset?: number,
    limit?: number
  ) {
    const plans = await AssessmentPlan.findAll({
      where: {
        enterpriseCode,
        semester,
        status: 'published',
      },
      attributes: ['planId'],
    });
    const planIds = plans.map(plan => plan.planId);
    const queryOpt: WhereOptions = {
      ...query,
      planId: planIds,
      month,
    };
    if (query.assessedName) {
      queryOpt.assessedName = {
        [Op.like]: `%${query.assessedName}%`,
      };
    }
    if (query.ruleName) {
      queryOpt.ruleName = {
        [Op.like]: `%${query.ruleName}%`,
      };
    }
    const pubPlans = await PublicationRecord.findOne({
      where: {
        planId: planIds,
        month,
      },
      attributes: ['planId'],
    });
    const { list } = await this.findAll({
      query: queryOpt,
      include: [
        {
          model: Scoring,
          attributes: ['weight'],
        },
        {
          model: Score,
          attributes: ['scoreId', 'scoreValue'],
          include: [
            {
              model: ObservationPoint,
              attributes: ['pointName', 'baseScore'],
            },
          ],
        },
      ],
      offset,
      limit,
      order: [
        ['assessedCode', 'asc'],
        ['assessorCode', 'asc'],
      ],
    });
    return {
      pubPlans,
      list,
    };
  }

  /**
   * 获取月度考核任务填写详情
   * @param enterpriseCode 学校编号
   * @param semester 学期编号
   * @param month 月份
   * @param query 筛选条件
   * @returns list
   */
  async getMonthlyTaskProgress(
    enterpriseCode: string,
    semester: string,
    month: number,
    query: Partial<IAssessmentTask>
  ) {
    const plans = await AssessmentPlan.findAll({
      where: {
        enterpriseCode,
        semester,
        status: 'published',
      },
      attributes: ['planId'],
    });
    const planIds = plans.map(plan => plan.planId);
    const queryOpt: WhereOptions = {
      ...query,
      planId: planIds,
      month,
    };
    if (query.assessorName) {
      queryOpt.assessorName = {
        [Op.like]: `%${query.assessorName}%`,
      };
    }
    if (query.ruleName) {
      queryOpt.ruleName = {
        [Op.like]: `%${query.ruleName}%`,
      };
    }
    const result = await AssessmentTask.findAll({
      where: queryOpt,
      attributes: [
        'assessorCode',
        'assessorName',
        'ruleName',
        [
          AssessmentTask.sequelize.literal(
            "SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END)"
          ),
          'completedCount',
        ],
        [
          AssessmentTask.sequelize.literal(
            "SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END)"
          ),
          'notCompletedCount',
        ],
        [AssessmentTask.sequelize.fn('COUNT', '*'), 'total'],
      ],
      group: ['assessorCode', 'assessorName', 'ruleName'],
      order: [['assessorCode', 'asc']],
    });
    return result;
  }

  /**
   * 计算用户列表
   * @param enterpriseCode 企业编码
   * @param type 类型:'role'|'user'|'mixed'
   * @param roles 角色列表
   * @param users 用户列表
   * @returns 用户列表
   */
  private async caclUserList({
    enterpriseCode,
    type,
    roles,
    users,
  }: {
    enterpriseCode: string;
    type: 'role' | 'user' | 'mixed' | 'question';
    roles?: { roleCode: string; roleName: string }[];
    users?: { userCode: string; userName: string }[];
  }) {
    const userService = this.userService;
    async function getListByRole() {
      // 角色类型需要查询角色下的用户
      const roleList = (roles || [])
        .filter(role => !!role)
        .map(role => ({
          code: role.roleCode,
          name: role.roleName,
        }));
      const array = await Promise.all(
        roleList.map(async role => {
          const users = await userService.getSchoolUserRoleList(
            enterpriseCode,
            role.code
          );
          console.log(`【${role.name}】角色有【${users.list.length}】个人`);
          return users.list.map(user => ({
            code: user.code,
            name: user.name,
          }));
        })
      );
      return _.uniqBy(_.flattenDeep(array), 'code');
    }

    function getListByUser() {
      // 用户类型直接使用users
      return (users || [])
        .filter(user => !!user)
        .map(user => ({
          code: user.userCode,
          name: user.userName,
        }));
    }

    let list: {
      code: string;
      name: string;
    }[] = [];

    switch (type) {
      case 'role':
        list = await getListByRole();
        break;
      case 'user':
        list = getListByUser();
        break;
      case 'mixed':
        {
          const list1 = await getListByRole();
          const list2 = getListByUser();
          list = _.uniqBy([...list1, ...list2], 'code');
        }
        break;
      default:
        // 家长问卷不创建任务
        break;
    }
    return list;
  }

  /**
   * 创建空的评分记录
   * @param scoringId 评分ID
   * @param taskId 任务ID
   * @param transaction 事务
   */
  private async createEmptyScores(
    scoringId: number,
    taskId: number,
    transaction: any
  ) {
    const points = await ObservationPoint.findAll({ where: { scoringId } });
    const scores = points.map(point => ({
      taskId,
      pointId: point.pointId,
    }));
    Score.bulkCreate(scores, { transaction });
  }

  /**
   * 验证并获取考核方案
   * @param planId 方案ID
   * @param month 月份
   * @returns 考核方案或null
   */
  private async validateAndGetPlan(
    planId: number,
    month: number
  ): Promise<AssessmentPlan | null> {
    const plan = await AssessmentPlan.findByPk(planId);
    if (!plan) {
      throw new CustomError('指定的考核方案不存在');
    }

    const startMonth = new Date(plan.startMonth).getMonth() + 1;
    const endMonth = new Date(plan.endMonth).getMonth() + 1;
    if (month < startMonth || month > endMonth) {
      this.logger.info({
        message: '考核月份不在方案有效期内，跳过创建任务',
        source: '业务',
        operation: 'generateMonthlyTasks',
        data: { planId, month },
      });
      return null;
    }

    return plan;
  }

  /**
   * 获取现有任务和规则
   * @param plan 考核方案
   * @param month 月份
   * @param transaction 事务
   * @returns 现有任务和规则
   */
  private async getExistingTasksAndRules(
    plan: AssessmentPlan,
    month: number,
    transaction: any
  ) {
    const { list: existingTasks } = await this.findAll({
      query: { planId: plan.planId, month },
      transaction,
    });

    const rules = await plan['getRules']();
    if (!rules?.length) {
      throw new CustomError('考核方案下没有规则');
    }

    return { existingTasks, rules };
  }

  /**
   * 处理规则并生成任务
   * @param plan 考核方案
   * @param month 月份
   * @param rules 规则列表
   * @param existingTasks 现有任务
   * @param transaction 事务
   * @returns 预期任务键和新任务
   */
  private async processRulesAndGenerateTasks(
    plan: AssessmentPlan,
    month: number,
    rules: any[],
    existingTasks: AssessmentTask[],
    transaction: any
  ) {
    const expectedTaskKeys = new Set<string>();
    const newTasks: AssessmentTask[] = [];

    for (const rule of rules) {
      const scorings = await rule['getScorings']();
      if (!scorings?.length) {
        console.log(`【${rule.ruleName}】规则下没有评分组`);
        continue;
      }

      for (const scoring of scorings as Scoring[]) {
        const assessors = await this.caclUserList({
          enterpriseCode: plan.enterpriseCode,
          type: scoring.assessorType,
          roles: scoring.roles,
          users: scoring.users,
        });
        console.log(
          `【${scoring.title}】评分组下有【${assessors.length}】个考核人`
        );

        // 家长问卷类型是没有考核人的，这种情况不需要再查被考核人
        const assessedUsers = assessors.length
          ? await this.caclUserList({
              enterpriseCode: plan.enterpriseCode,
              type: rule.assessedType,
              roles: rule.roles,
              users: rule.users,
            })
          : [];
        console.log(
          `【${rule.title}】规则下有【${assessedUsers.length}】个被考核人`
        );

        await this.createTasksForUsers(
          plan.planId,
          month,
          rule,
          scoring,
          assessors,
          assessedUsers,
          existingTasks,
          expectedTaskKeys,
          newTasks,
          transaction
        );
      }
    }

    return { expectedTaskKeys, newTasks };
  }

  /**
   * 为用户创建任务
   * @param planId 方案ID
   * @param month 月份
   * @param rule 规则
   * @param scoring 评分
   * @param assessors 考核人列表
   * @param assessedUsers 被考核人列表
   * @param existingTasks 现有任务
   * @param expectedTaskKeys 预期任务键
   * @param newTasks 新任务列表
   * @param transaction 事务
   */
  private async createTasksForUsers(
    planId: number,
    month: number,
    rule: Rule,
    scoring: Scoring,
    assessors: { code: string; name: string }[],
    assessedUsers: { code: string; name: string }[],
    existingTasks: AssessmentTask[],
    expectedTaskKeys: Set<string>,
    newTasks: AssessmentTask[],
    transaction: any
  ) {
    for (const assessor of assessors) {
      for (const assessed of assessedUsers) {
        // 排除自己给自己打分
        if (assessor.code === assessed.code) continue;

        const taskKey = `${planId}-${month}-${assessor.code}-${assessed.code}`;
        expectedTaskKeys.add(taskKey);

        const existingTask = this.findExistingTask(
          existingTasks,
          planId,
          month,
          assessor.code,
          assessed.code
        );

        if (existingTask) {
          this.logger.debug({
            message: `任务已存在，跳过创建: ${taskKey}`,
            source: '业务',
            operation: 'generateMonthlyTasks',
            data: { taskId: existingTask.taskId },
          });
          continue;
        }

        const task = await this.createNewTask(
          planId,
          rule,
          scoring,
          month,
          assessor,
          assessed,
          transaction
        );
        newTasks.push(task);
      }
    }
  }

  /**
   * 查找现有任务
   * @param existingTasks 现有任务列表
   * @param planId 方案ID
   * @param month 月份
   * @param assessorCode 考核人编码
   * @param assessedCode 被考核人编码
   * @returns 任务或undefined
   */
  private findExistingTask(
    existingTasks: AssessmentTask[],
    planId: number,
    month: number,
    assessorCode: string,
    assessedCode: string
  ): AssessmentTask | undefined {
    return existingTasks.find(
      task =>
        task.planId === planId &&
        task.month === month &&
        task.assessorCode === assessorCode &&
        task.assessedCode === assessedCode
    );
  }

  /**
   * 创建新任务
   * @param planId 方案ID
   * @param scoring 规则
   * @param scoring 评分
   * @param month 月份
   * @param assessor 考核人
   * @param assessed 被考核人
   * @param transaction 事务
   * @returns 新创建的任务
   */
  private async createNewTask(
    planId: number,
    rule: Rule,
    scoring: Scoring,
    month: number,
    assessor: { code: string; name: string },
    assessed: { code: string; name: string },
    transaction: any
  ): Promise<AssessmentTask> {
    console.time(`创建任务: ${assessor.code}-${assessed.code}`);
    const task = await this.create(
      {
        planId,
        ruleId: rule.ruleId,
        scoringId: scoring.scoringId,
        month,
        assessorCode: assessor.code,
        assessorName: assessor.name,
        assessedCode: assessed.code,
        assessedName: assessed.name,
        /** 评分规则名称 */
        ruleName: rule.title,
        /** 赋分规则名称 */
        scoringName: scoring.title,
        status: 'pending',
      },
      transaction
    );
    await this.createEmptyScores(scoring.scoringId, task.taskId, transaction);
    console.timeEnd(`创建任务: ${assessor.code}-${assessed.code}`);
    return task;
  }

  /**
   * 清理冗余任务
   * @param existingTasks 现有任务
   * @param expectedTaskKeys 预期任务键
   * @param transaction 事务
   */
  private async cleanupRedundantTasks(
    existingTasks: AssessmentTask[],
    expectedTaskKeys: Set<string>,
    transaction: any
  ) {
    const tasksToDelete = existingTasks.filter(task => {
      const taskKey = `${task.planId}-${task.month}-${task.assessorCode}-${task.assessedCode}`;
      return !expectedTaskKeys.has(taskKey);
    });

    if (tasksToDelete.length > 0) {
      const taskIdsToDelete = tasksToDelete.map(task => task.taskId);
      await this.deleteRedundantTasks(taskIdsToDelete, transaction);
    }
  }

  /**
   * 删除冗余任务
   * @param taskIdsToDelete 要删除的任务ID列表
   * @param transaction 事务
   */
  private async deleteRedundantTasks(
    taskIdsToDelete: number[],
    transaction: any
  ) {
    this.logger.info({
      message: `删除${taskIdsToDelete.length}个多余的任务`,
      source: '业务',
      operation: 'generateMonthlyTasks',
      data: { taskIds: taskIdsToDelete },
    });

    await Score.destroy({
      where: { taskId: { [Op.in]: taskIdsToDelete } },
      transaction,
    });

    await AssessmentTask.destroy({
      where: { taskId: { [Op.in]: taskIdsToDelete } },
      transaction,
    });
  }

  /**
   * 获取最终任务列表
   * @param existingTasks 现有任务
   * @param newTasks 新任务
   * @param expectedTaskKeys 预期任务键
   * @returns 最终任务列表
   */
  private getFinalTaskList(
    existingTasks: AssessmentTask[],
    newTasks: AssessmentTask[],
    expectedTaskKeys: Set<string>
  ): AssessmentTask[] {
    return [
      ...existingTasks.filter(task => {
        const taskKey = `${task.planId}-${task.month}-${task.assessorCode}-${task.assessedCode}`;
        return expectedTaskKeys.has(taskKey);
      }),
      ...newTasks,
    ];
  }

  /**
   * 记录任务生成摘要
   * @param plan 考核方案
   * @param month 月份
   * @param existingTasks 现有任务
   * @param newTasks 新任务
   * @param allTasks 所有任务
   */
  private logTaskGenerationSummary(
    plan: AssessmentPlan,
    month: number,
    existingTasks: AssessmentTask[],
    newTasks: AssessmentTask[],
    allTasks: AssessmentTask[]
  ) {
    const logInfo: LoggerMsgProps = {
      message: '考核任务列表处理完成',
      source: '业务',
      operation: 'generateMonthlyTasks',
      data: {
        方案名称: plan.planName,
        月份: month,
        已存在任务数: existingTasks.length,
        新增任务数: newTasks.length,
        删除任务数: existingTasks.length - (allTasks.length - newTasks.length),
        最终任务数: allTasks.length,
      },
    };
    this.logger.info(logInfo);
    console.dir(logInfo);
    return logInfo;
  }

  /**
   * 记录错误信息
   * @param error 错误对象
   * @param planId 方案ID
   * @param month 月份
   */
  private logError(error: any, planId: number, month: number) {
    console.error('生成月度考核任务失败');
    console.error(error);
    this.logger.error(
      {
        message: `生成月度考核任务失败: ${error.message}`,
        source: '业务',
        operation: 'generateMonthlyTasks',
        data: { planId, month },
      },
      error
    );
  }

  /**
   * 根据考核方案生成指定月份的考核任务
   * 支持多次调用场景，会对比已存在的任务，只创建缺少的任务，删除多余的任务
   * @param planId 考核方案ID
   * @param month 考核月份
   * @returns 生成的考核任务列表
   */
  async generateMonthlyTasks(
    planId: number,
    month: number
  ): Promise<LoggerMsgProps> {
    // 验证考核方案并获取方案详情
    const plan = await this.validateAndGetPlan(planId, month);
    if (!plan) {
      return {
        message: '考核月份不在方案有效期内',
        source: '业务',
        operation: 'generateMonthlyTasks',
        data: {
          方案ID: planId,
          月份: month,
        },
      };
    }

    // 开启数据库事务
    const transaction = await AssessmentTask.sequelize.transaction();

    try {
      // 记录开始创建任务的日志
      this.logger.info({
        message: '开始创建任务',
        source: '业务',
        operation: 'generateMonthlyTasks',
        data: { planId, name: plan.planName, month },
      });

      // 获取现有任务和考核规则
      const { existingTasks, rules } = await this.getExistingTasksAndRules(
        plan,
        month,
        transaction
      );

      // 处理规则并生成新任务
      const { expectedTaskKeys, newTasks } =
        await this.processRulesAndGenerateTasks(
          plan,
          month,
          rules,
          existingTasks,
          transaction
        );

      // 清理多余的任务
      await this.cleanupRedundantTasks(
        existingTasks,
        expectedTaskKeys,
        transaction
      );

      // 提交事务
      await transaction.commit();

      // 获取最终的任务列表
      const allTasks = this.getFinalTaskList(
        existingTasks,
        newTasks,
        expectedTaskKeys
      );

      // 记录任务生成摘要日志
      const returnInfo = this.logTaskGenerationSummary(
        plan,
        month,
        existingTasks,
        newTasks,
        allTasks
      );

      return returnInfo;
    } catch (error) {
      // 发生错误时回滚事务
      await transaction.rollback();
      // 记录错误日志
      this.logError(error, planId, month);
      throw error;
    }
  }

  /**
   * 删除指定方案下指定月的所有任务，为防止误删，月份必传
   * @param planId 方案ID
   * @param month 月份（时间戳）
   */
  async deleteTasksByPlanIdAndMonth(planId: number, month: number[]) {
    try {
      // 构建查询条件
      const where = {
        planId,
        month: {
          [Op.in]: month,
        },
      };
      // 执行删除操作
      await this.delete(where);
    } catch (error) {
      this.logger.error(
        {
          message: `删除任务失败: ${error.message}`,
          source: '数据库',
          operation: 'deleteTasksByPlanIdAndMonth',
          data: { planId, month },
        },
        error
      );
      throw new CustomError(`删除任务失败: ${error.message}`);
    }
  }

  /**
   * 验证任务有效性
   * @param task 任务信息
   */
  public async validateTask(task: Partial<AssessmentTask>): Promise<void> {
    // 验证考核方案是否存在
    const plan = await AssessmentPlan.findByPk(task.planId);
    if (!plan) {
      throw new CustomError('指定的考核方案不存在');
    }

    // 验证考核月份是否在方案有效期内
    const taskMonth = new Date(task.month);
    if (taskMonth < plan.startMonth || taskMonth > plan.endMonth) {
      throw new CustomError('考核月份不在方案有效期内');
    }

    // 设置默认状态为待完成
    if (!task.status) {
      task.status = 'pending';
    }
  }
}
