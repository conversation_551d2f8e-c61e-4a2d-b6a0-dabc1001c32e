# 教师月度考评系统

基于 Midway.js 框架开发的教师月度考核评价系统，支持考核方案配置、自动任务生成、多维度评分、第三方问卷集成和结果公示等功能。

## 主要功能

- 📋 **考核方案管理**: 创建、配置、发布考核方案
- 🔄 **方案复制**: 基于现有方案快速创建新方案（新增功能）
- 📝 **考核任务**: 根据方案自动生成月度考核任务
- ⭐ **评分系统**: 支持分值评分、星级评分、备注、附件
- 📊 **成绩报表**: 生成考核报表、Excel导出、进度统计
- 🔗 **问卷集成**: 与第三方问卷系统集成
- 📢 **公示管理**: 考核结果公示记录管理

## 快速开始

详细信息请参考 [midway 文档][midway]。

### Development

```bash
$ npm i
$ npm run dev
$ open http://localhost:3140/
```

### Deploy

```bash
$ npm start
```

### npm scripts

- Use `npm run lint` to check code style.
- Use `npm test` to run unit test.

## 新功能亮点

### 🔄 复制考核方案

支持基于现有方案创建新方案，包括：

- **深度复制**: 复制方案及其所有关联数据（规则、赋分规则、观测点）
- **智能命名**: 自动处理唯一约束冲突，避免名称重复
- **数据隔离**: 确保复制的数据完全独立，不影响原方案

```bash
# API 示例
POST /assessment-plans/123/copy
{
  "planName": "2024年下学期教师考核方案",
  "semester": "2024202502",
  "description": "基于上学期方案复制的新方案"
}
```

## 文档

- [开发说明](开发说明.md) - 详细的开发文档
- [API接口文档](docs/api-reference.md) - 完整的API接口说明
- [复制方案功能](docs/copy-assessment-plan.md) - 复制功能详细说明
- [更新日志](docs/CHANGELOG.md) - 版本更新记录

[midway]: https://midwayjs.org
