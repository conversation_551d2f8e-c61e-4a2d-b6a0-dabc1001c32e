import { MidwayConfig } from '@midwayjs/core';

export default {
  // 用于 cookie 签名密钥，应更改为您自己的并保持安全
  keys: '1742867117121_7145',
  koa: {
    port: 3140,
  },
  sequelize: {
    dataSource: {
      default: {
        dialect: 'mysql',
        host: '**********',
        port: 3306,
        username: 'root',
        password: 'Ysp@1234',
        database: 'teacher-evaluation',
        // 模型设置
        define: {
          timestamps: true,
          createdAt: 'createdAt',
          updatedAt: 'updatedAt',
          // 禁用驼峰转换
          underscored: false,
          charset: 'utf8mb4',
          collate: 'utf8mb4_bin',
        },
        // 时区设置
        timezone: '+08:00',
        // 连接池配置
        pool: {
          max: 5,
          min: 0,
          idle: 10000,
        },
        entities: ['entity'],
        busboy: {
          mode: 'file',
          whitelist: ['.docx', '.xlsx'],
          // cleanTimeout: number，上传的文件在临时目录中多久之后自动删除，默认为 5 分钟
          cleanTimeout: 5 * 60 * 1000,
          limits: {
            fileSize: 1024 * 1024 * 10, // 文件最大上传大小 10MB
          },
          defParamCharset: 'utf8', // 默认参数编码格式
        },
        logging: false,
      },
    },
  },
  // 日志配置
  midwayLogger: {
    default: {
      // 日志输出级别
      level: 'info',
      // 日志文件配置
      transports: {
        file: {
          maxFiles: '7d',
        },
        error: {
          maxFiles: '7d',
        },
        console: false,
        json: {
          fileLogName: 'teacher-evaluation.json.log',
        },
      },
    },
  },
  // 请求日志中间件配置
  requestLogger: {
    // 排除不需要记录日志的路径
    // excludePaths: ['/health', '/favicon.ico', '/metrics*'],
    // 是否记录请求体
    logRequestBody: true,
    // 是否记录响应体（可能会导致日志过大，建议仅在开发环境启用）
    logResponseBody: false,
  },
  axios: {
    default: {
      timeout: 30000,
    },
    clients: {
      // 接口转发中心配置
      apiManager: {
        baseURL: 'http://***********:1002',
      },
      // 教师评价对应的问卷系统
      questionnaire: {
        baseURL: 'http://***********:3141',
      },
    },
  },
  bullmq: {
    defaultConnection: {
      host: '************',
      port: 6379,
      password: 'carryredis',
    },
    // 可选，队列前缀
    defaultPrefix: '{teacher-evaluation-bullmq}',
  },
} as MidwayConfig;
