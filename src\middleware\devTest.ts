/*
 * @description: 开发测试接口中间件
 * @author: zpl
 * @Date: 2024-06-08 20:12:14
 * @LastEditTime: 2024-11-07 12:15:44
 * @LastEditors: 朱鹏亮 <EMAIL>
 */
import { Middleware, IMiddleware } from '@midwayjs/core';
import { NextFunction, Context } from '@midwayjs/koa';
import { ErrorCode } from '../common/ErrorCode';

@Middleware()
export class DevTestMiddleware implements IMiddleware<Context, NextFunction> {
  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      const env = ctx.getApp().getEnv();
      if (!['local', 'unittest'].includes(env)) {
        return {
          errCode: ErrorCode.BIZ_ERROR,
          msg: '请使用本地或测试环境进行测试',
        };
      }
      return await next();
    };
  }

  // match(ctx) {
  //   return ctx.path.indexOf('/api') === 0;
  // }

  static getName(): string {
    return 'API_RESPONSE_FORMAT';
  }
}
