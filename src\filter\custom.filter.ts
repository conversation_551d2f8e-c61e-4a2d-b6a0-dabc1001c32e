/*
 * @Description: 业务自定义错误过滤器
 * @Date: 2025-01-07 17:54:13
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-03-28 15:27:35
 */
import { Catch, MidwayHttpError } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';

@Catch(CustomError)
export class CustomErrorFilter {
  async catch(err: MidwayHttpError, ctx: Context) {
    console.log('err', err);
    // 所有的自定义错误会到这里
    ctx.logger.error(err);
    return { errCode: err.status, msg: err.message };
  }
}
