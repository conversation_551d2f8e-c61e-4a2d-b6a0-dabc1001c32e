import { parse, build, WorkSheetOptions } from 'node-xlsx';

/** excel导入列配置 */
export type ExcelColumnImportOptions = {
  /** 标题，填写excel中的列名，如：姓名，年龄，性别 */
  title: string;
  /** 返回英文名称，不指定则使用title，目的支持定制返回的json数据中的key */
  enName?: string;
  /** 数据类型，暂时支持string,number，默认string */
  type?: 'string' | 'number';
  /** 最大长度，默认不限制，仅对string类型有效 */
  maxLength?: number;
  /** 正则匹配，仅对string类型有效 */
  regex?: RegExp;
  /** 最大值，仅对number类型有效 */
  max?: number;
  /** 最小值，仅对number类型有效 */
  min?: number;
  /** 是否允许为空，默认不允许 */
  allowEmpty?: boolean;
};

/** excel导出列配置 */
export type ExcelColumnExportOptions = {
  /** 标题，填写excel中的列名，如：姓名，年龄，性别 */
  title: string;
  /** 中文名称，不填写则使用title，目的支持定制导出的excel中的列名 */
  cnName?: string;
  /** 数据类型，暂时支持string,number，默认string */
  type?: 'string' | 'number';
  /** 返回英文名称，不指定则使用title，目的支持定制返回的json数据中的key */
  enName?: string;
  /** 宽度，默认自动 */
  width?: number;
};

export class ExcelUtil {
  /**
   * 按opts配置信息解析excel，成功返回json数据，失败返回错误提示
   *
   * @static
   * @param {Buffer | string} buffers 文件流
   * @param {object} opts 解析配置
   * @param {number} [opts.startLineNo=1] 开始行号,默认1
   * @param {ExcelColumnImportOptions[]} opts.columns 统计列配置
   * @return {{data: object[], errInfo: object}} res
   * @memberof ExcelUtil
   */
  static readToJson(
    buffers: Buffer | string,
    opts: { startLineNo?: number; columns: ExcelColumnImportOptions[] }
  ) {
    const { startLineNo = 1, columns } = opts || {};
    const { data: sourceData } = parse(buffers, { blankrows: true })[0];
    // 过滤前置需要忽略的行
    if (startLineNo > 1) {
      sourceData.splice(0, startLineNo - 1);
    }

    const headers = sourceData[0];
    const headerIndex = {};
    // 校验列
    for (const col of columns) {
      if (!headers.includes(col.title)) {
        return {
          data: [],
          errInfo: {
            msg: `表格结构不合法，缺少'${col.title}'字段，请使用系统提供的模板`,
          },
        };
      }
      headerIndex[col.title] = headers.indexOf(col.title);
    }
    // 参与循环的数据去掉表头
    sourceData.shift();

    const data: any[] = [];
    const errInfo = {};
    sourceData.forEach((row, index) => {
      if (row.length === 0) {
        // 跳过空行
        return;
      }
      const obj = {};
      const rowNum = index + startLineNo + 1;
      // 检查数据合法性
      columns.forEach(column => {
        const conf = {
          type: 'string',
          ...column,
        };
        let value = row[headerIndex[column.title]];
        // 如果类型为string，删除前后空格和换行
        if (value && conf.type === 'string') {
          value = String(value).trim();
        }
        // 判断空
        if (!conf.allowEmpty && value !== 0 && !value) {
          errInfo[rowNum] = errInfo[rowNum] || {};
          errInfo[rowNum][column.title] = '不允许为空';
          return;
        }
        // 允许为空时数据可能为空
        if (value) {
          switch (conf.type) {
            case 'string':
              // 判断最大长度
              if (conf.maxLength && value.length > conf.maxLength) {
                errInfo[rowNum] = errInfo[rowNum] || {};
                errInfo[rowNum][
                  column.title
                ] = `最大长度不能超过${conf.maxLength}`;
                return;
              }
              // 判断正则
              if (conf.regex && !new RegExp(conf.regex).test(value)) {
                errInfo[rowNum] = errInfo[rowNum] || {};
                errInfo[rowNum][column.title] = '格式不正确';
              }
              break;
            case 'number':
              // 判断是否为数字
              value = Number(value);
              if (isNaN(value)) {
                errInfo[rowNum] = errInfo[rowNum] || {};
                errInfo[rowNum][column.title] = '必须为数字';
                return;
              }
              // 判断最大值
              if (conf.max && value > conf.max) {
                errInfo[rowNum] = errInfo[rowNum] || {};
                errInfo[rowNum][column.title] = `不能大于${conf.max}`;
                return;
              }
              // 判断最小值
              if (conf.min && value < conf.min) {
                errInfo[rowNum] = errInfo[rowNum] || {};
                errInfo[rowNum][column.title] = `不能小于${conf.min}`;
                return;
              }
              break;
            default:
              break;
          }
        }
        obj[column.enName || column.title] = value;
      });
      if (errInfo[rowNum]) {
        return;
      }
      data.push(obj);
    });
    console.log(
      `Excel数据分析完成，${
        Object.keys(errInfo).length ? '有' : '无'
      }错误，解析数据完成${data.length}条`
    );
    return {
      data,
      errInfo,
    };
  }

  /**
   * 根据json数据生成excel文件buffer
   *
   * @static
   * @param {any[]} data json数据
   * @param {{columns: ExcelColumnExportOptions[], filename: string}} opts 配置项
   * @memberof ExcelUtil
   */
  static async writeFromJson(
    data: {
      sheetName?: string;
      columns: ExcelColumnExportOptions[];
      headers?: string[][];
      data: any[];
      options?: WorkSheetOptions;
    }[]
  ) {
    const sheetList = data.map((item, i) => {
      const { sheetName, columns = [[]], headers, data, options } = item;
      console.log('Excel数据生成中...');
      const header =
        headers ||
        columns.map(item => item.map(col => col.cnName || col.title));
      return {
        name: sheetName || `sheet${i + 1}`,
        data: [
          ...header,
          ...data.map(row =>
            columns.map(col => row[col.enName || col.title] || '')
          ),
        ],
        options: options || {},
      };
    });
    console.log('Excel数据生成完成');
    // console.log(sheetList[0].data);
    const buffer = build(sheetList);
    return buffer;
  }
}
