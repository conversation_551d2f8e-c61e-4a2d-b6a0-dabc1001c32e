import { Controller, Get, Inject, Param, Query } from '@midwayjs/core';
import { Custome } from '../service/api_3th/custome.service';

@Controller('/schoolInfo/:enterpriseCode')
export class SchoolInfoController {
  @Inject()
  custome: Custome;

  @Get('/semester', { summary: '获取学期列表' })
  async getSemesterList(@Param('enterpriseCode') enterpriseCode: string) {
    return this.custome.getSemesterList(enterpriseCode);
  }

  @Get('/schoolRoles', { summary: '获取角色列表' })
  async getSchoolRoles(@Param('enterpriseCode') enterpriseCode: string) {
    return this.custome.getSchoolRoles(enterpriseCode);
  }

  @Get('/:roleCode/users', { summary: '获取指定角色下成员列表' })
  async getSchoolUserRoleList(
    @Param('enterpriseCode') enterpriseCode: string,
    @Param('roleCode') roleCode: string,
    @Query() query: { limit: number; offset: number }
  ) {
    return this.custome.getSchoolUserRoleList(
      enterpriseCode,
      roleCode,
      query.limit,
      query.offset
    );
  }

  @Get('/users', { summary: '获取指定学校下的所有成员' })
  async getMembers(@Param('enterpriseCode') enterpriseCode: string) {
    return this.custome.getMembers(enterpriseCode);
  }
}
